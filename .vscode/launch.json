{"version": "2.0.0", "configurations": [{"name": "Python Debugger: Current File with Arguments", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "args": "-g ~/qnx/hardware/camsys isp.patch"}, {"type": "qnx-gdb", "request": "launch", "name": "QNX: apimonitor", "gdb": "gdb", "program": "${command:qnx.autodetectBinary}", "remotePath": "apimonitor", "qnxtarget": "default", "upload": true, "stopAtEntry": true, "preLaunchTask": "${defaultBuildTask}"}, {"type": "qnx-gdb", "request": "launch", "name": "build/aarch64le-debug/api_monitor", "gdb": "gdb", "program": "build/aarch64le-debug/api_monitor", "remotePath": "api_monitor", "qnxtarget": "default", "upload": true, "stopAtEntry": true, "preLaunchTask": "${defaultBuildTask}"}, {"type": "qnx-gdb", "request": "launch", "name": "test_alloc", "gdb": "gdb", "program": "test_alloc", "remotePath": "test_alloc", "qnxtarget": "default", "upload": true, "stopAtEntry": true, "preLaunchTask": "${defaultBuildTask}"}, {"type": "qnx-gdb", "request": "launch", "name": "memmon", "gdb": "gdb", "program": "memmon", "remotePath": "memmon", "qnxtarget": "default", "upload": true, "stopAtEntry": true, "preLaunchTask": "${defaultBuildTask}"}]}