void* fun_start() {
    
    double start, end;
    void* ptr;
    
    int should_monitor = monitoring;
    
    if (should_monitor) {
        start = get_time_ns();
        ptr = real_malloc(size);
        end = get_time_ns();
        
        pthread_mutex_lock(&stats_mutex);
        malloc_count++;
        total_malloc_time += (end - start);
        
        // Update size-based statistics
        int bucket = get_bucket_index(size);
        if (bucket < NUM_BUCKETS) {
            malloc_buckets[bucket].count++;
            malloc_buckets[bucket].total_time += (end - start);
        }
        pthread_mutex_unlock(&stats_mutex);
        
        // Store pointer and size for later free tracking
    } 
    return ptr;
}

fun_end()
{

}