#define _GNU_SOURCE
#include <stdio.h>
#include <dlfcn.h>
#include <stdlib.h>
#include <pthread.h>
#include <time.h>
#include <signal.h>
#include <unistd.h>
#include <string.h>

#include "base_hooks.h"

static struct timespec start_time;
static struct timespec end_time;
static int monitoring = 1;
static int max_func = 1;



static pthread_mutex_t stats_mutex = PTHREAD_MUTEX_INITIALIZER;

static HookStats fc_stats[MAX_FUNC] = {0, 0.0, 0.0, 0.0};

HookStats *get_fstats(int index)
{
    return &fc_stats[index];
}

void set_fname(const int index, const char*fname)
{
    strncpy(fc_stats[index].name,  "malloc", sizeof(fc_stats[index].name) - 1);
}


void set_maxfunc(const int max)
{
    max_func = max;
}
// Function to print statistics to a specified output
static void print_statistics(FILE *output) {
    pthread_mutex_lock(&stats_mutex);
    fprintf(output, "\n=== Memory Allocation Statistics ===\n");
    for (int i = 0; i < max_func; i++) {
            fprintf(output, "%s: count=%ld, total=%.3fus, avg=%.3fus, max=%.3fus, min=%.3fus\n",
                    fc_stats[i].name, fc_stats[i].count, fc_stats[i].total_time,
                    fc_stats[i].count ? fc_stats[i].total_time / fc_stats[i].count : 0,
                    fc_stats[i].max_time, fc_stats[i].min_time);
    }
    pthread_mutex_unlock(&stats_mutex);
}

// Signal handler for SIGUSR1, SIGUSR2, and SIGURG
static void signal_handler(int sig) {
    if (sig == SIGUSR1) {
        print_statistics(stderr);
    } else if (sig == SIGUSR2) {
        // Toggle monitoring
        pthread_mutex_lock(&stats_mutex);
        monitoring = !monitoring;
        fprintf(stderr, "\n=== Memory monitoring %s ===\n", 
                monitoring ? "resumed" : "paused");
        pthread_mutex_unlock(&stats_mutex);
    } else if (sig == SIGURG) {
        // Save statistics to file when requested (e.g., on Ctrl-C)
        //save_statistics_to_file();
    }
    if (sig == SIGINT)
        exit(0);
}

void static_init()
{
        // Set up signal handlers
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    signal(SIGURG, signal_handler);  // Add handler for SIGURG
    signal(SIGINT, signal_handler);  // Add handler for SIGURG
    
    // Record start time
    clock_gettime(CLOCK_MONOTONIC, &start_time);
    
    fprintf(stderr, "Memory allocation monitoring started (PID: %d)\n", getpid());
    fprintf(stderr, "Send SIGUSR1 to get statistics\n");
    fprintf(stderr, "Send SIGUSR2 to toggle monitoring\n");

    for (int i = 0; i < MAX_FUNC; i++) {
        fc_stats[i].count = 0;
        fc_stats[i].total_time = 0.0;
        fc_stats[i].max_time = 0.0;
        fc_stats[i].min_time = 10000;
        pthread_mutex_init(&fc_stats[i].stats_mutex, NULL);
    }

}

__attribute__((destructor))
static void cleanup(void) {
    fprintf(stdout, "exit:");
    print_statistics(stdout);
    //save_statistics_to_file();
}

#ifdef __TEST
void main()
{
    while(1) {
        char *p = malloc(100);
        free(p);
        usleep(100000);
    }
}
#endif