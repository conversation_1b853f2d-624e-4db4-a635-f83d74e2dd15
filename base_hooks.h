
// Statistics for each hook function
typedef struct {
    char name[100];  // Function names, adjust size as needed
    long count;
    double total_time;
    double max_time;
    double min_time;
    pthread_mutex_t stats_mutex;
} HookStats;


#define MAX_FUNC 100  // Adjust based on the number of functions you want to track

HookStats *get_fstats(int index);
void set_fname(const int index, const char*fname);
void set_maxfunc(const int max);
void static_init(void);


static inline void pre_call(int index, struct timespec *call_start_time)
{

    clock_gettime(CLOCK_MONOTONIC, call_start_time);
}

static inline void post_call(int index, struct timespec *call_start_time)
{
    
    struct timespec end_time;
    clock_gettime(CLOCK_MONOTONIC, &end_time);
    
    double elapsed = (end_time.tv_sec - call_start_time->tv_sec) * 1e6 +
                    (end_time.tv_nsec - call_start_time->tv_nsec) / 1e3;
    
    HookStats *stats = get_fstats(index);
    
    pthread_mutex_lock(&stats->stats_mutex);
    stats->count++;
    stats->total_time += elapsed;
    if (elapsed > stats->max_time) {
        stats->max_time = elapsed;
    }
    if (elapsed < stats->min_time) {
        stats->min_time = elapsed;
    }
    pthread_mutex_unlock(&stats->stats_mutex);
}

