#!/usr/bin/env python3
import sys
import re
import os

class cfile:
    def __init__(self):
        self.common_line = []
        self.linux_line = []
        self.qnx_line = []

    # read C file and fill 3 arrays, each element is (start_line, end_line)
    def analysis(self, filepath):
        """
        Analyze a C file to identify Linux-specific, QNX-specific, and common code blocks.
        Each entry in the arrays is a tuple of (start_line_number, end_line_number).
        """
        if not os.path.exists(filepath):
            print(f"Error: File '{filepath}' not found")
            return False
            
        with open(filepath, 'r') as f:
            lines = f.readlines()
        
        in_linux_block = False
        in_qnx_block = False
        current_block_start = 0
        
        # Stack to track nested ifdefs
        linux_stack = []
        qnx_stack = []
        
        for i, line in enumerate(lines):
            line_num = i + 1  # Line numbers start at 1
            
            # Check for beginning of OS-specific blocks
            if "#ifdef __linux__" in line or "#if defined(__linux__)" in line:
                if not in_linux_block and not in_qnx_block:
                    # If we were in a common block, add it to common_line
                    if current_block_start > 0:
                        self.common_line.append((current_block_start, line_num - 1))
                
                in_linux_block = True
                linux_stack.append(line_num)
                current_block_start = line_num + 1
                
            elif "#ifdef __QNX__" in line or "#if defined(__QNX__)" in line:
                if not in_linux_block and not in_qnx_block:
                    # If we were in a common block, add it to common_line
                    if current_block_start > 0:
                        self.common_line.append((current_block_start, line_num - 1))
                
                in_qnx_block = True
                qnx_stack.append(line_num)
                current_block_start = line_num + 1
                
            # Check for nested #else blocks
            elif "#else" in line:
                if in_linux_block:
                    # End the Linux block
                    if current_block_start > 0:
                        self.linux_line.append((current_block_start, line_num - 1))
                    
                    # Start a QNX block (assuming #else after #ifdef __linux__ is for QNX)
                    in_linux_block = False
                    in_qnx_block = True
                    current_block_start = line_num + 1
                    
                elif in_qnx_block:
                    # End the QNX block
                    if current_block_start > 0:
                        self.qnx_line.append((current_block_start, line_num - 1))
                    
                    # Start a Linux block (assuming #else after #ifdef __QNX__ is for Linux)
                    in_qnx_block = False
                    in_linux_block = True
                    current_block_start = line_num + 1
            
            # Check for end of OS-specific blocks
            elif "#endif" in line:
                if in_linux_block:
                    if linux_stack:
                        # End the Linux block
                        if current_block_start > 0:
                            self.linux_line.append((current_block_start, line_num - 1))
                        linux_stack.pop()
                        
                        # If we're out of nested Linux blocks, reset
                        if not linux_stack:
                            in_linux_block = False
                            current_block_start = line_num + 1
                            
                elif in_qnx_block:
                    if qnx_stack:
                        # End the QNX block
                        if current_block_start > 0:
                            self.qnx_line.append((current_block_start, line_num - 1))
                        qnx_stack.pop()
                        
                        # If we're out of nested QNX blocks, reset
                        if not qnx_stack:
                            in_qnx_block = False
                            current_block_start = line_num + 1
            
            # Start tracking from the beginning if we haven't started yet
            if current_block_start == 0 and not in_linux_block and not in_qnx_block:
                current_block_start = line_num
        
        # Add the final block if we're still in one
        if current_block_start > 0:
            if in_linux_block:
                self.linux_line.append((current_block_start, len(lines)))
            elif in_qnx_block:
                self.qnx_line.append((current_block_start, len(lines)))
            else:
                self.common_line.append((current_block_start, len(lines)))
        
        return True

    def check_line(self, line_number):
        """
        Check which category a specific line number belongs to.
        Returns:
            0: Common code
            1: Linux-specific code
            2: QNX-specific code
        """
        # Check if line is in Linux blocks
        for start, end in self.linux_line:
            if start <= line_number <= end:
                return 1
                
        # Check if line is in QNX blocks
        for start, end in self.qnx_line:
            if start <= line_number <= end:
                return 2
                
        # Check if line is in common blocks
        for start, end in self.common_line:
            if start <= line_number <= end:
                return 0
                
        # Default to common if not found
        return 0


def check_filenames(patch_file):
    """
    Check if the patch modifies files with OS-specific indicators in their names.
    Returns:
        0: Both Linux and QNX files or neither
        1: Only Linux files
        2: Only QNX files
        None: No OS indicators in filenames, further analysis needed
    """
    with open(patch_file, 'r') as f:
        patch_content = f.read()
    
    # Extract modified file paths
    file_pattern = re.compile(r'^\+\+\+ b/(.+?)$', re.MULTILINE)
    modified_files = file_pattern.findall(patch_content)
    for fname in modified_files:
        print(fname)
    
    # Check filenames for OS indicators
    linux_files = [f for f in modified_files if "_linux" in os.path.basename(f)]
    qnx_files = [f for f in modified_files if "_qnx" in os.path.basename(f)]
    
    # Case 1: Check filenames
    if linux_files and not qnx_files:
        return 1
    elif qnx_files and not linux_files:
        return 2
    elif linux_files and qnx_files:
        return 0
    
    # No OS indicators in filenames
    return None, patch_content

def analyze_patch(patch_file, git_root):
    """
    Analyze a git patch file to determine OS-specific changes.
    Returns:
        0: Changes for both Linux and QNX or neither
        1: Changes only for Linux
        2: Changes only for QNX
    """
    # First check filenames for OS indicators
    result, patch_content = check_filenames(patch_file)
    if result is not None:
        return result
        
    # Case 2: No OS indicators in filenames, check code blocks
    linux_changes = False
    qnx_changes = False
    
    # Extract hunks from the patch
    hunks = re.split(r'diff --git', patch_content)
    
    for hunk in hunks[1:]:  # Skip the first empty split
        # Get the file being modified
        file_match = re.search(r'^\+\+\+ b/(.+?)$', hunk, re.MULTILINE)
        if not file_match:
            continue
            
        file_path = file_match.group(1)
        
        # Skip non-source files
        if not file_path.endswith(('.c', '.cpp', '.h', '.hpp')):
            continue
        
        # Get the original file path (a/file)
        orig_file_match = re.search(r'^--- a/(.+?)$', hunk, re.MULTILINE)
        if not orig_file_match:
            continue
            
        orig_file_path = orig_file_match.group(1)
        full_path = os.path.join(git_root, orig_file_path)

        added_lines = re.findall(r'^\+(?!\+\+)(.*)$', hunk, re.MULTILINE)
        ifstart = False
        for line in added_lines:
            if "#ifdef __linux__" in line or "#if defined(__linux__)" or "#elif defined(__linux__)" in line:
                linux_changes = True
                ifstart = True
            elif "#ifdef __QNX__" in line or "#if defined(__QNX__)" or "#elif defined(__QNX__)" in line:
                qnx_changes = True
                ifstart = True
            elif "#else" in line:
                if ifstart:
                    if linux_changes:
                        qnx_changes = True
                    if qnx_changes:
                        linux_changes = True
            elif "#endif" in line:
                ifstart = False
            elif line.strip() and not line.startswith('#'):
                # Non-preprocessor line outside known blocks
                # Consider it as affecting both OSes
                linux_changes = True
                qnx_changes = True
            continue
        
        if not os.path.exists(full_path):
            print(f"File '{full_path}' not found. Trying relative path...")
            full_path = os.path.join(os.getcwd(), orig_file_path)
        
        # Use cfile to analyze the source file
        cf = cfile()
        if not cf.analysis(full_path):
            continue
        
        # Extract line numbers of added/modified lines from the hunk
        line_numbers = []
        hunk_headers = re.findall(r'^@@ -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@', hunk, re.MULTILINE)
        
        for header in hunk_headers:
            start_line = int(header[1])  # Start line in new file
            
            # Count lines in this hunk
            hunk_start = hunk.find(f"@@ -{header[0]}")
            next_hunk_start = hunk.find("@@ -", hunk_start + 1)
            if next_hunk_start == -1:
                hunk_content = hunk[hunk_start:]
            else:
                hunk_content = hunk[hunk_start:next_hunk_start]
            
            # Find added lines in this hunk
            current_line = start_line
            for line in hunk_content.split('\n'):
                if line.startswith('+') and not line.startswith('+++'):
                    line_numbers.append(current_line)
                if not line.startswith('-'):  # Don't increment line number for removed lines
                    current_line += 1
        
        # Check which OS each modified line belongs to
        linux_only = False
        qnx_only = False
        common_changes = False
        
        for line_num in line_numbers:
            line_type = cf.check_line(line_num)
            if line_type == 0:
                common_changes = True
            elif line_type == 1:
                linux_only = True
            elif line_type == 2:
                qnx_only = True
        
        # Update the overall changes flags
        if linux_only and not qnx_only and not common_changes:
            linux_changes = True
        elif qnx_only and not linux_only and not common_changes:
            qnx_changes = True
        elif common_changes or (linux_only and qnx_only):
            # If there are common changes or both Linux and QNX changes,
            # consider it as affecting both OSes
            linux_changes = True
            qnx_changes = True
    
    # Determine result based on code changes
    if linux_changes and not qnx_changes:
        return 1
    elif qnx_changes and not linux_changes:
        return 2
    else:
        # Both or neither OS affected
        return 0

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Analyze a git patch file for OS-specific changes')
    parser.add_argument('patch_file', help='Path to the git patch file')
    parser.add_argument('-g', '--git-root', help='Path to the root of the git repository')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.patch_file):
        print(f"Error: Patch file '{args.patch_file}' not found")
        sys.exit(1)
    
    # Store the original working directory
    original_dir = os.getcwd()
    
    # Change to git root directory if specified
    if args.git_root:
        if not os.path.exists(args.git_root):
            print(f"Error: Git root directory '{args.git_root}' not found")
            sys.exit(1)
    
    try:
        result = analyze_patch(args.patch_file, args.git_root)
        print("Result:", result)
        sys.exit(result)
    finally:
        # Change back to the original directory
        os.chdir(original_dir)

if __name__ == "__main__":
    main()
