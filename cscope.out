cscope 15 $HOME/qnx/apimonitor -c               0000035364
	@.vscode/preinclude.h

1 #undef 
__linux__


	@iotest.c

1 #include 
	~<stdio.h
>

2 #include 
	~<stdlib.h
>

3 #include 
	~<string.h
>

4 #include 
	~<unistd.h
>

5 #include 
	~<fcntl.h
>

6 #include 
	~<sys/types.h
>

7 #include 
	~<sys/socket.h
>

8 #include 
	~<netinet/in.h
>

9 #include 
	~<arpa/inet.h
>

10 #include 
	~<time.h
>

13 void 
	$test_file_operations
(const char *
filename
, int 
num_writes
) {

14 
	`printf
("Testing file operations with %s...\n", 
filename
);

17 int 
fd
 = 
	`open
(
filename
, 
O_WRONLY
 | 
O_CREAT
 | 
O_TRUNC
, 0644);

18 if (
fd
 < 0) {

19 
	`perror
("Failed to open file");

24 char 
buffer
[1024];

25 for (int 
i
 = 0; i < 
num_writes
; i++) {

26 
	`snprintf
(
buffer
, sizeof(buffer), "This is test write #%d to file %s\n", 
i
+1, 
filename
);

27 
ssize_t
 
bytes
 = 
	`write
(
fd
, 
buffer
, 
	`strlen
(buffer));

28 if (
bytes
 < 0) {

29 
	`perror
("Failed to write to file");

30 
	`close
(
fd
);

35 
	`usleep
(10000);

39 
	`close
(
fd
);

41 
	`printf
("Completed file operations with %s\n", 
filename
);

42 
	}
}

45 void 
	$test_socket_operations
(const char *
ip
, int 
port
, int 
num_sends
) {

46 
	`printf
("Testing socket operations to %s:%d...\n", 
ip
, 
port
);

49 int 
sockfd
 = 
	`socket
(
AF_INET
, 
SOCK_DGRAM
, 0);

50 if (
sockfd
 < 0) {

51 
	`perror
("Failed to create socket");

56 struct 
sockaddr_in
 
server_addr
;

57 
	`memset
(&
server_addr
, 0, sizeof(server_addr));

58 
server_addr
.
sin_family
 = 
AF_INET
;

59 
server_addr
.
sin_port
 = 
	`htons
(
port
);

61 if (
	`inet_pton
(
AF_INET
, 
ip
, &
server_addr
.
sin_addr
) <= 0) {

62 
	`perror
("Invalid address");

63 
	`close
(
sockfd
);

68 char 
buffer
[1024];

69 for (int 
i
 = 0; i < 
num_sends
; i++) {

70 
	`snprintf
(
buffer
, sizeof(buffer), "Test send #%d", 
i
+1);

71 
ssize_t
 
bytes
 = 
	`send
(
sockfd
, 
buffer
, 
	`strlen
(buffer), 0);

72 if (
bytes
 < 0) {

73 
	`perror
("Failed to send data");

74 
	`close
(
sockfd
);

78 
	`usleep
(10000);

82 for (int 
i
 = 0; i < 
num_sends
; i++) {

83 
	`snprintf
(
buffer
, sizeof(buffer), "Test sendto #%d", 
i
+1);

84 
ssize_t
 
bytes
 = 
	`sendto
(
sockfd
, 
buffer
, 
	`strlen
(buffer), 0,

85 (struct 
sockaddr
*)&
server_addr
, sizeof(server_addr));

86 if (
bytes
 < 0) {

87 
	`perror
("Failed to sendto data");

88 
	`close
(
sockfd
);

92 
	`usleep
(10000);

96 for (int 
i
 = 0; i < 
num_sends
; i++) {

97 struct 
msghdr
 
msg
;

98 struct 
iovec
 
iov
;

100 
	`snprintf
(
buffer
, sizeof(buffer), "Test sendmsg #%d", 
i
+1);

102 
iov
.
iov_base
 = 
buffer
;

103 
iov
.
iov_len
 = 
	`strlen
(
buffer
);

105 
	`memset
(&
msg
, 0, sizeof(msg));

106 
msg
.
msg_name
 = &
server_addr
;

107 
msg
.
msg_namelen
 = sizeof(
server_addr
);

108 
msg
.
msg_iov
 = &
iov
;

109 
msg
.
msg_iovlen
 = 1;

111 
ssize_t
 
bytes
 = 
	`sendmsg
(
sockfd
, &
msg
, 0);

112 if (
bytes
 < 0) {

113 
	`perror
("Failed to sendmsg data");

114 
	`close
(
sockfd
);

118 
	`usleep
(10000);

122 
	`close
(
sockfd
);

124 
	`printf
("Completed socket operations\n");

125 
	}
}

128 void 
	$test_different_sizes
() {

129 
	`printf
("Testing writes with different sizes...\n");

132 int 
fd
 = 
	`open
("size_test.dat", 
O_WRONLY
 | 
O_CREAT
 | 
O_TRUNC
, 0644);

133 if (
fd
 < 0) {

134 
	`perror
("Failed to open file");

139 for (
size_t
 
size
 = 2; size <= 1024*1024*2; size *= 2) {

140 char *
buffer
 = 
	`malloc
(
size
);

141 if (!
buffer
) {

142 
	`perror
("Failed to allocate buffer");

143 
	`close
(
fd
);

148 
	`memset
(
buffer
, 'A' + (
size
 % 26), size);

150 
	`printf
(" Writing %zu bytes...\n", 
size
);

151 
ssize_t
 
bytes
 = 
	`write
(
fd
, 
buffer
, 
size
);

152 if (
bytes
 < 0) {

153 
	`perror
("Failed to write to file");

154 
	`free
(
buffer
);

155 
	`close
(
fd
);

159 
	`free
(
buffer
);

160 
	`usleep
(50000);

164 
	`close
(
fd
);

166 
	`printf
("Completed size tests\n");

167 
	}
}

170 void 
	$test_multiple_fds
(int 
num_files
) {

171 
	`printf
("Testing operations with multiple file descriptors...\n");

173 int *
fds
 = 
	`malloc
(
num_files
 * sizeof(int));

174 if (!
fds
) {

175 
	`perror
("Failed to allocate FD array");

180 for (int 
i
 = 0; i < 
num_files
; i++) {

181 char 
filename
[64];

182 
	`snprintf
(
filename
, sizeof(filename), "test_file_%d.txt", 
i
);

184 
fds
[
i
] = 
	`open
(
filename
, 
O_WRONLY
 | 
O_CREAT
 | 
O_TRUNC
, 0644);

185 if (
fds
[
i
] < 0) {

186 
	`perror
("Failed to open file");

188 for (int 
j
 = 0; j < 
i
; j++) {

189 
	`close
(
fds
[
j
]);

191 
	`free
(
fds
);

197 char 
buffer
[1024];

198 for (int 
i
 = 0; i < 
num_files
; i++) {

199 
	`snprintf
(
buffer
, sizeof(buffer), "This is test data for file %d\n", 
i
);

200 
	`write
(
fds
[
i
], 
buffer
, 
	`strlen
(buffer));

201 
	`usleep
(10000);

205 for (int 
i
 = 0; i < 
num_files
; i++) {

206 
	`close
(
fds
[
i
]);

209 
	`free
(
fds
);

210 
	`printf
("Completed multiple FD test\n");

211 
	}
}

213 int 
	$main
(int 
argc
, char *
argv
[]) {

214 
	`printf
("I/O Monitoring Test Program\n");

215 
	`printf
("---------------------------\n");

218 
	`test_file_operations
("test_file.txt", 5);

222 
	`test_socket_operations
("127.0.0.1", 12345, 3);

225 
	`test_different_sizes
();

228 
	`test_multiple_fds
(5);

230 
	`printf
("\nAll tests completed. Press Enter to exit...\n");

231 
	`getchar
();

234 
	}
}

	@iotrace.c

1 #define 
	#_GNU_SOURCE


	)

2 #include 
	~<stdio.h
>

3 #include 
	~<stdlib.h
>

4 #include 
	~<dlfcn.h
>

5 #include 
	~<time.h
>

6 #include 
	~<pthread.h
>

7 #include 
	~<unistd.h
>

8 #include 
	~<signal.h
>

9 #include 
	~<string.h
>

10 #include 
	~<sys/time.h
>

11 #include 
	~<sys/types.h
>

12 #include 
	~<sys/socket.h
>

13 #include 
	~<fcntl.h
>

14 #include 
	~<errno.h
>

17 static int (*
real_open
)(const char *, int, ...) = 
NULL
;

18 static int (*
real_close
)(int) = 
NULL
;

19 static int (*
real_socket
)(int, int, int) = 
NULL
;

20 static 
	$ssize_t
 (*
real_write
)(int, const void *, 
size_t
) = 
NULL
;

21 static 
	$ssize_t
 (*
real_send
)(int, const void *, 
size_t
, int) = 
NULL
;

22 static 
	$ssize_t
 (*
real_sendto
)(int, const void *, 
size_t
, int, const struct 
sockaddr
 *, 
socklen_t
) = 
NULL
;

23 static 
	$ssize_t
 (*
real_sendmsg
)(int, const struct 
msghdr
 *, int) = 
NULL
;

26 #define 
	#MAX_FD
 1024

	)

30 int 
is_active
;

31 int 
is_socket
;

32 char 
path
[256];

33 
size_t
 
open_count
;

34 
size_t
 
close_count
;

35 
size_t
 
write_count
;

36 
size_t
 
send_count
;

37 
size_t
 
sendto_count
;

38 
size_t
 
sendmsg_count
;

39 
size_t
 
total_bytes_written
;

40 double 
total_write_time
;

41 double 
total_send_time
;

42 } 
	tfd_stats_t
;

45 static 
fd_stats_t
 
fd_stats
[
MAX_FD
] = {0
	}
};

46 static 
size_t
 
	gtotal_open_count
 = 0;

47 static 
size_t
 
	gtotal_socket_count
 = 0;

48 static 
size_t
 
	gtotal_close_count
 = 0;

49 static 
size_t
 
	gtotal_write_count
 = 0;

50 static 
size_t
 
	gtotal_send_count
 = 0;

51 static 
size_t
 
	gtotal_sendto_count
 = 0;

52 static 
size_t
 
	gtotal_sendmsg_count
 = 0;

53 static 
size_t
 
	gtotal_bytes_written
 = 0;

54 static double 
	gtotal_open_time
 = 0.0;

55 static double 
	gtotal_close_time
 = 0.0;

56 static double 
	gtotal_socket_time
 = 0.0;

57 static double 
	gtotal_write_time
 = 0.0;

58 static double 
	gtotal_send_time
 = 0.0;

60 static struct 
timespec
 
	gstart_time
;

61 static int 
	gmonitoring
 = 1;

62 static 
pthread_mutex_t
 
	gstats_mutex
 = 
PTHREAD_MUTEX_INITIALIZER
;

65 static double 
	$get_time_ns
() {

66 struct 
timespec
 
ts
;

67 
	`clock_gettime
(
CLOCK_MONOTONIC
, &
ts
);

68 return (double)
ts
.
tv_sec
 * 1e9 + (double)ts.
tv_nsec
;

69 
	}
}

72 static double 
	$elapsed_seconds
() {

73 struct 
timespec
 
now
;

74 
	`clock_gettime
(
CLOCK_MONOTONIC
, &
now
);

75 return (
now
.
tv_sec
 - 
start_time
.tv_sec) +

76 (
now
.
tv_nsec
 - 
start_time
.tv_nsec) / 1e9;

77 
	}
}

80 static void 
print_statistics
(
FILE
 *
output
);

83 static int 
	$save_statistics_to_file
() {

85 
time_t
 
now
 = 
	`time
(
NULL
);

86 struct 
tm
 *
tm_info
 = 
	`localtime
(&
now
);

88 char 
filename
[64];

89 
	`strftime
(
filename
, sizeof(filename), "io-stat-%H%M%S.txt", 
tm_info
);

91 
FILE
 *
file
 = 
	`fopen
(
filename
, "w");

92 if (
file
 == 
NULL
) {

93 
	`fprintf
(
stderr
, "Error: Could not create statistics file %s\n", 
filename
);

97 
	`fprintf
(
stderr
, "Saving I/O statistics to %s\n", 
filename
);

98 
	`print_statistics
(
file
);

99 
	`fclose
(
file
);

101 
	}
}

104 static void 
	$signal_handler
(int 
sig
) {

105 if (
sig
 == 
SIGUSR1
) {

106 
	`print_statistics
(
stderr
);

107 } else if (
sig
 == 
SIGUSR2
) {

109 
	`pthread_mutex_lock
(&
stats_mutex
);

110 
monitoring
 = !monitoring;

111 
	`fprintf
(
stderr
, "\n=== I/O monitoring %s ===\n",

112 
monitoring
 ? "resumed" : "paused");

113 
	`pthread_mutex_unlock
(&
stats_mutex
);

114 } else if (
sig
 == 
SIGURG
) {

116 
	`save_statistics_to_file
();

118 
	}
}

121 static void 
	$print_statistics
(
FILE
 *
output
) {

122 double 
elapsed
 = 
	`elapsed_seconds
();

124 
	`fprintf
(
output
, "\n=== I/O Operation Statistics ===\n");

125 
	`fprintf
(
output
, "Elapsed time: %.2f seconds\n", 
elapsed
);

126 
	`fprintf
(
output
, "Total open calls: %zu\n", 
total_open_count
);

127 
	`fprintf
(
output
, "Total socket calls: %zu\n", 
total_socket_count
);

128 
	`fprintf
(
output
, "Total close calls: %zu\n", 
total_close_count
);

129 
	`fprintf
(
output
, "Total write calls: %zu\n", 
total_write_count
);

130 
	`fprintf
(
output
, "Total send calls: %zu\n", 
total_send_count
);

131 
	`fprintf
(
output
, "Total sendto calls: %zu\n", 
total_sendto_count
);

132 
	`fprintf
(
output
, "Total sendmsg calls: %zu\n", 
total_sendmsg_count
);

133 
	`fprintf
(
output
, "Total bytes written: %zu\n", 
total_bytes_written
);

135 double 
avg_open_time
 = 
total_open_count
 > 0 ? 
total_open_time
 / total_open_count / 1e3 : 0;

136 double 
avg_close_time
 = 
total_close_count
 > 0 ? 
total_close_time
 / total_close_count / 1e3 : 0;

137 double 
avg_socket_time
 = 
total_socket_count
 > 0 ? 
total_socket_time
 / total_socket_count / 1e3 : 0;

138 double 
avg_write_time
 = 
total_write_count
 > 0 ? 
total_write_time
 / total_write_count / 1e3 : 0;

139 double 
avg_send_time
 = (
total_send_count
 + 
total_sendto_count
 + 
total_sendmsg_count
) > 0 ?

140 
total_send_time
 / (
total_send_count
 + 
total_sendto_count
 + 
total_sendmsg_count
) / 1e3 : 0;

142 
	`fprintf
(
output
, "Average open time: %.4f useconds\n", 
avg_open_time
);

143 
	`fprintf
(
output
, "Average close time: %.4f useconds\n", 
avg_close_time
);

144 
	`fprintf
(
output
, "Average socket time: %.4f useconds\n", 
avg_socket_time
);

145 
	`fprintf
(
output
, "Average write time: %.4f useconds\n", 
avg_write_time
);

146 
	`fprintf
(
output
, "Average send time: %.4f useconds\n", 
avg_send_time
);

149 
	`fprintf
(
output
, "\n=== Per File Descriptor Statistics ===\n");

150 
	`fprintf
(
output
, "FD | Type | Path/Description | Open | Close | Write | Send | Bytes Written\n");

151 
	`fprintf
(
output
, "----+--------+---------------------------------------+------+-------+-------+------+--------------\n");

153 for (int 
fd
 = 0; fd < 
MAX_FD
; fd++) {

154 if (
fd_stats
[
fd
].
open_count
 > 0 || fd_stats[fd].
is_active
) {

156 
size_t
 
total_sends
 = 
fd_stats
[
fd
].
send_count
 +

157 
fd_stats
[
fd
].
sendto_count
 +

158 
fd_stats
[
fd
].
sendmsg_count
;

160 
	`fprintf
(
output
, "%3d | %-6s | %-37s | %4zu | %5zu | %5zu | %4zu | %12zu\n",

161 
fd
,

162 
fd_stats
[
fd
].
is_socket
 ? "Socket" : "File",

163 
fd_stats
[
fd
].
path
,

164 
fd_stats
[
fd
].
open_count
,

165 
fd_stats
[
fd
].
close_count
,

166 
fd_stats
[
fd
].
write_count
,

167 
total_sends
,

168 
fd_stats
[
fd
].
total_bytes_written
);

172 
	`fprintf
(
output
, "===================================\n");

173 
	}
}

176 
__attribute__
((
constructor
))

177 static void 
	$init
(void) {

179 
real_open
 = 
	`dlsym
(
RTLD_NEXT
, "open");

180 
real_close
 = 
	`dlsym
(
RTLD_NEXT
, "close");

181 
real_socket
 = 
	`dlsym
(
RTLD_NEXT
, "socket");

182 
real_write
 = 
	`dlsym
(
RTLD_NEXT
, "write");

183 
real_send
 = 
	`dlsym
(
RTLD_NEXT
, "send");

184 
real_sendto
 = 
	`dlsym
(
RTLD_NEXT
, "sendto");

185 
real_sendmsg
 = 
	`dlsym
(
RTLD_NEXT
, "sendmsg");

187 if (!
real_open
 || !
real_close
 || !
real_socket
 || !
real_write
 ||

188 !
real_send
 || !
real_sendto
 || !
real_sendmsg
) {

189 
	`fprintf
(
stderr
, "Error: Failed to get original function pointers\n");

190 
	`exit
(1);

194 
	`signal
(
SIGUSR1
, 
signal_handler
);

195 
	`signal
(
SIGUSR2
, 
signal_handler
);

196 
	`signal
(
SIGURG
, 
signal_handler
);

199 
	`clock_gettime
(
CLOCK_MONOTONIC
, &
start_time
);

201 
	`fprintf
(
stderr
, "I/O operation monitoring started (PID: %d)\n", 
	`getpid
());

202 
	`fprintf
(
stderr
, "Send SIGUSR1 to get statistics\n");

203 
	`fprintf
(
stderr
, "Send SIGUSR2 to toggle monitoring\n");

206 
	`strcpy
(
fd_stats
[0].
path
, "stdin");

207 
fd_stats
[0].
is_active
 = 1;

208 
	`strcpy
(
fd_stats
[1].
path
, "stdout");

209 
fd_stats
[1].
is_active
 = 1;

210 
	`strcpy
(
fd_stats
[2].
path
, "stderr");

211 
fd_stats
[2].
is_active
 = 1;

212 
	}
}

215 
__attribute__
((
destructor
))

216 static void 
	$cleanup
(void) {

217 
	`print_statistics
(
stderr
);

218 
	`save_statistics_to_file
();

219 
	}
}

222 int 
	$open
(const char *
pathname
, int 
flags
, ...) {

223 if (!
real_open
) {

224 
real_open
 = 
	`dlsym
(
RTLD_NEXT
, "open");

227 
mode_t
 
mode
 = 0;

228 if (
flags
 & 
O_CREAT
) {

229 
va_list
 
args
;

230 
	`va_start
(
args
, 
flags
);

231 
mode
 = 
	`va_arg
(
args
, 
mode_t
);

232 
	`va_end
(
args
);

235 double 
start
, 
end
;

236 int 
fd
;

238 int 
should_monitor
 = 
monitoring
;

240 if (
should_monitor
) {

241 
start
 = 
	`get_time_ns
();

242 
fd
 = 
	`real_open
(
pathname
, 
flags
, 
mode
);

243 
end
 = 
	`get_time_ns
();

245 if (
fd
 >= 0 && fd < 
MAX_FD
) {

246 
	`pthread_mutex_lock
(&
stats_mutex
);

247 
total_open_count
++;

248 
total_open_time
 += (
end
 - 
start
);

250 
fd_stats
[
fd
].
is_active
 = 1;

251 
fd_stats
[
fd
].
is_socket
 = 0;

252 
fd_stats
[
fd
].
open_count
++;

253 
	`strncpy
(
fd_stats
[
fd
].
path
, 
pathname
, sizeof(fd_stats[fd].path) - 1);

254 
fd_stats
[
fd
].
path
[sizeof(fd_stats[fd].path) - 1] = '\0';

255 
	`pthread_mutex_unlock
(&
stats_mutex
);

258 
fd
 = 
	`real_open
(
pathname
, 
flags
, 
mode
);

261 return 
fd
;

262 
	}
}

265 int 
	$socket
(int 
domain
, int 
type
, int 
protocol
) {

266 if (!
real_socket
) {

267 
real_socket
 = 
	`dlsym
(
RTLD_NEXT
, "socket");

270 double 
start
, 
end
;

271 int 
fd
;

273 int 
should_monitor
 = 
monitoring
;

275 if (
should_monitor
) {

276 
start
 = 
	`get_time_ns
();

277 
fd
 = 
	`real_socket
(
domain
, 
type
, 
protocol
);

278 
end
 = 
	`get_time_ns
();

280 if (
fd
 >= 0 && fd < 
MAX_FD
) {

281 
	`pthread_mutex_lock
(&
stats_mutex
);

282 
total_socket_count
++;

283 
total_socket_time
 += (
end
 - 
start
);

285 
fd_stats
[
fd
].
is_active
 = 1;

286 
fd_stats
[
fd
].
is_socket
 = 1;

287 
fd_stats
[
fd
].
open_count
++;

290 char 
desc
[256];

291 
	`snprintf
(
desc
, sizeof(desc), "socket(domain=%d, type=%d, protocol=%d)",

292 
domain
, 
type
, 
protocol
);

293 
	`strncpy
(
fd_stats
[
fd
].
path
, 
desc
, sizeof(fd_stats[fd].path) - 1);

294 
fd_stats
[
fd
].
path
[sizeof(fd_stats[fd].path) - 1] = '\0';

295 
	`pthread_mutex_unlock
(&
stats_mutex
);

298 
fd
 = 
	`real_socket
(
domain
, 
type
, 
protocol
);

301 return 
fd
;

302 
	}
}

305 int 
	$close
(int 
fd
) {

306 if (!
real_close
) {

307 
real_close
 = 
	`dlsym
(
RTLD_NEXT
, "close");

310 double 
start
, 
end
;

311 int 
result
;

313 int 
should_monitor
 = 
monitoring
;

315 if (
should_monitor
 && 
fd
 >= 0 && fd < 
MAX_FD
) {

316 
start
 = 
	`get_time_ns
();

317 
result
 = 
	`real_close
(
fd
);

318 
end
 = 
	`get_time_ns
();

320 
	`pthread_mutex_lock
(&
stats_mutex
);

321 
total_close_count
++;

322 
total_close_time
 += (
end
 - 
start
);

324 if (
result
 == 0) {

325 
fd_stats
[
fd
].
is_active
 = 0;

326 
fd_stats
[
fd
].
close_count
++;

328 
	`pthread_mutex_unlock
(&
stats_mutex
);

330 
result
 = 
	`real_close
(
fd
);

333 return 
result
;

334 
	}
}

337 
ssize_t
 
	$write
(int 
fd
, const void *
buf
, 
size_t
 
count
) {

338 if (!
real_write
) {

339 
real_write
 = 
	`dlsym
(
RTLD_NEXT
, "write");

342 double 
start
, 
end
;

343 
ssize_t
 
result
;

345 int 
should_monitor
 = 
monitoring
;

347 if (
should_monitor
 && 
fd
 >= 0 && fd < 
MAX_FD
) {

348 
start
 = 
	`get_time_ns
();

349 
result
 = 
	`real_write
(
fd
, 
buf
, 
count
);

350 
end
 = 
	`get_time_ns
();

352 if (
result
 > 0) {

353 
	`pthread_mutex_lock
(&
stats_mutex
);

354 
total_write_count
++;

355 
total_write_time
 += (
end
 - 
start
);

356 
total_bytes_written
 += 
result
;

358 
fd_stats
[
fd
].
write_count
++;

359 
fd_stats
[
fd
].
total_bytes_written
 += 
result
;

360 
fd_stats
[
fd
].
total_write_time
 += (
end
 - 
start
);

361 
	`pthread_mutex_unlock
(&
stats_mutex
);

364 
result
 = 
	`real_write
(
fd
, 
buf
, 
count
);

367 return 
result
;

368 
	}
}

371 
ssize_t
 
	$send
(int 
sockfd
, const void *
buf
, 
size_t
 
len
, int 
flags
) {

372 if (!
real_send
) {

373 
real_send
 = 
	`dlsym
(
RTLD_NEXT
, "send");

376 double 
start
, 
end
;

377 
ssize_t
 
result
;

379 int 
should_monitor
 = 
monitoring
;

381 if (
should_monitor
 && 
sockfd
 >= 0 && sockfd < 
MAX_FD
) {

382 
start
 = 
	`get_time_ns
();

383 
result
 = 
	`real_send
(
sockfd
, 
buf
, 
len
, 
flags
);

384 
end
 = 
	`get_time_ns
();

386 if (
result
 > 0) {

387 
	`pthread_mutex_lock
(&
stats_mutex
);

388 
total_send_count
++;

389 
total_send_time
 += (
end
 - 
start
);

390 
total_bytes_written
 += 
result
;

392 
fd_stats
[
sockfd
].
send_count
++;

393 
fd_stats
[
sockfd
].
total_bytes_written
 += 
result
;

394 
fd_stats
[
sockfd
].
total_send_time
 += (
end
 - 
start
);

395 
	`pthread_mutex_unlock
(&
stats_mutex
);

398 
result
 = 
	`real_send
(
sockfd
, 
buf
, 
len
, 
flags
);

401 return 
result
;

402 
	}
}

405 
ssize_t
 
	$sendto
(int 
sockfd
, const void *
buf
, 
size_t
 
len
, int 
flags
,

406 const struct 
sockaddr
 *
dest_addr
, 
socklen_t
 
addrlen
) {

407 if (!
real_sendto
) {

408 
real_sendto
 = 
	`dlsym
(
RTLD_NEXT
, "sendto");

411 double 
start
, 
end
;

412 
ssize_t
 
result
;

414 int 
should_monitor
 = 
monitoring
;

416 if (
should_monitor
 && 
sockfd
 >= 0 && sockfd < 
MAX_FD
) {

417 
start
 = 
	`get_time_ns
();

418 
result
 = 
	`real_sendto
(
sockfd
, 
buf
, 
len
, 
flags
, 
dest_addr
, 
addrlen
);

419 
end
 = 
	`get_time_ns
();

421 if (
result
 > 0) {

422 
	`pthread_mutex_lock
(&
stats_mutex
);

423 
total_sendto_count
++;

424 
total_send_time
 += (
end
 - 
start
);

425 
total_bytes_written
 += 
result
;

427 
fd_stats
[
sockfd
].
sendto_count
++;

428 
fd_stats
[
sockfd
].
total_bytes_written
 += 
result
;

429 
fd_stats
[
sockfd
].
total_send_time
 += (
end
 - 
start
);

430 
	`pthread_mutex_unlock
(&
stats_mutex
);

433 
result
 = 
	`real_sendto
(
sockfd
, 
buf
, 
len
, 
flags
, 
dest_addr
, 
addrlen
);

436 return 
result
;

437 
	}
}

440 
ssize_t
 
	$sendmsg
(int 
sockfd
, const struct 
msghdr
 *
msg
, int 
flags
) {

441 if (!
real_sendmsg
) {

442 
real_sendmsg
 = 
	`dlsym
(
RTLD_NEXT
, "sendmsg");

445 double 
start
, 
end
;

446 
ssize_t
 
result
;

448 int 
should_monitor
 = 
monitoring
;

450 if (
should_monitor
 && 
sockfd
 >= 0 && sockfd < 
MAX_FD
) {

451 
start
 = 
	`get_time_ns
();

452 
result
 = 
	`real_sendmsg
(
sockfd
, 
msg
, 
flags
);

453 
end
 = 
	`get_time_ns
();

455 if (
result
 > 0) {

456 
	`pthread_mutex_lock
(&
stats_mutex
);

457 
total_sendmsg_count
++;

458 
total_send_time
 += (
end
 - 
start
);

459 
total_bytes_written
 += 
result
;

461 
fd_stats
[
sockfd
].
sendmsg_count
++;

462 
fd_stats
[
sockfd
].
total_bytes_written
 += 
result
;

463 
fd_stats
[
sockfd
].
total_send_time
 += (
end
 - 
start
);

464 
	`pthread_mutex_unlock
(&
stats_mutex
);

467 
result
 = 
	`real_sendmsg
(
sockfd
, 
msg
, 
flags
);

470 return 
result
;

471 
	}
}

	@memmon.c

1 #include 
	~<stdio.h
>

2 #include 
	~<stdlib.h
>

3 #include 
	~<string.h
>

4 #include 
	~<unistd.h
>

5 #include 
	~<signal.h
>

6 #include 
	~<sys/types.h
>

7 #include 
	~<sys/wait.h
>

8 #include 
	~<libgen.h
>

9 #include 
	~<limits.h
>

10 #include 
	~<errno.h
>

11 #include 
	~<termios.h
>

12 #include 
	~<fcntl.h
>

14 
pid_t
 
	gchild_pid
 = -1;

17 void 
	$cleanup
(int 
signum
) {

18 if (
child_pid
 > 0) {

19 
	`printf
("Terminating monitored process...\n");

22 
	`kill
(
child_pid
, 
SIGUSR1
);

26 
	`kill
(
child_pid
, 
SIGURG
);

29 
	`usleep
(200000);

32 
	`kill
(
child_pid
, 
SIGTERM
);

34 
	`exit
(0);

35 
	}
}

38 int 
	$getch
() {

39 struct 
termios
 
old_tio
, 
new_tio
;

40 int 
ch
;

43 
	`tcgetattr
(
STDIN_FILENO
, &
old_tio
);

46 
new_tio
 = 
old_tio
;

49 
new_tio
.
c_lflag
 &= ~(
ICANON
 | 
ECHO
);

52 
	`tcsetattr
(
STDIN_FILENO
, 
TCSANOW
, &
new_tio
);

55 
ch
 = 
	`getchar
();

58 
	`tcsetattr
(
STDIN_FILENO
, 
TCSANOW
, &
old_tio
);

60 return 
ch
;

61 
	}
}

63 int 
	$main
(int 
argc
, char *
argv
[]) {

65 if (
argc
 < 2) {

66 
	`printf
("Usage: %s <program> [program arguments...]\n", 
argv
[0]);

71 char 
path
[
PATH_MAX
];

72 char *
dir
;

75 if (
argv
[0][0] == '/') {

77 
	`strncpy
(
path
, 
argv
[0], 
PATH_MAX
 - 1);

78 
path
[
PATH_MAX
 - 1] = '\0';

79 } else if (
	`strchr
(
argv
[0], '/') != 
NULL
) {

81 char 
cwd
[
PATH_MAX
];

82 if (
	`getcwd
(
cwd
, 
PATH_MAX
) == 
NULL
) {

83 
	`perror
("Failed to get current working directory");

86 
	`snprintf
(
path
, 
PATH_MAX
, "%s/%s", 
cwd
, 
argv
[0]);

89 char *
path_env
 = 
	`getenv
("PATH");

90 if (
path_env
 == 
NULL
) {

91 
	`fprintf
(
stderr
, "PATH environment variable not set\n");

96 char *
path_copy
 = 
	`strdup
(
path_env
);

97 if (
path_copy
 == 
NULL
) {

98 
	`perror
("Memory allocation failed");

102 char *
dir_path
 = 
	`strtok
(
path_copy
, ":");

103 int 
found
 = 0;

105 while (
dir_path
 != 
NULL
) {

106 
	`snprintf
(
path
, 
PATH_MAX
, "%s/%s", 
dir_path
, 
argv
[0]);

107 if (
	`access
(
path
, 
X_OK
) == 0) {

108 
found
 = 1;

111 
dir_path
 = 
	`strtok
(
NULL
, ":");

114 
	`free
(
path_copy
);

116 if (!
found
) {

118 if (
	`getcwd
(
path
, 
PATH_MAX
) == 
NULL
) {

119 
	`perror
("Failed to get current working directory");

126 
dir
 = 
	`dirname
(
path
);

129 char 
memtrace_path
[
PATH_MAX
];

130 
	`snprintf
(
memtrace_path
, 
PATH_MAX
, "%s/memtrace.so", 
dir
);

133 struct 
stat
 
memtrace_stat
;

135 if (
	`stat
(
memtrace_path
, &
memtrace_stat
) == -1) {

136 
	`printf
("memtrace.so does not exist\n");

137 
	`exit
(1);

141 char 
ld_preload
[
PATH_MAX
 * 2];

142 
	`snprintf
(
ld_preload
, 
PATH_MAX
 * 2, "LD_PRELOAD=%s", 
memtrace_path
);

145 char **
new_env
 = 
	`malloc
(sizeof(char *) * 2);

146 if (!
new_env
) {

147 
	`perror
("Memory allocation failed");

150 
new_env
[0] = 
ld_preload
;

151 
new_env
[1] = 
NULL
;

154 
	`printf
("Starting program with memory monitoring...\n");

156 
child_pid
 = 
	`fork
();

157 if (
child_pid
 == -1) {

158 
	`perror
("Fork failed");

159 
	`free
(
new_env
);

163 if (
child_pid
 == 0) {

165 
	`execve
(
argv
[1], &argv[1], 
new_env
);

168 
	`perror
("execve failed");

169 
	`exit
(1);

173 
	`free
(
new_env
);

176 
	`signal
(
SIGINT
, 
cleanup
);

177 
	`signal
(
SIGTERM
, 
cleanup
);

180 
	`printf
("Memory monitoring active for PID: %d\n", 
child_pid
);

181 
	`printf
("Commands:\n");

182 
	`printf
(" s - Show current statistics\n");

183 
	`printf
(" p - Pause/resume monitoring\n");

184 
	`printf
(" q - Quit and show final statistics\n");

187 int 
status
;

188 
pid_t
 
wait_result
;

189 int 
cmd
;

193 
wait_result
 = 
	`waitpid
(
child_pid
, &
status
, 
WNOHANG
);

194 if (
wait_result
 == 
child_pid
) {

195 
	`printf
("Monitored process has terminated.\n");

197 } else if (
wait_result
 == -1) {

198 
	`perror
("waitpid failed");

203 
cmd
 = 
	`getch
();

205 switch (
cmd
) {

207 
	`printf
("Requesting statistics...\n");

208 
	`kill
(
child_pid
, 
SIGUSR1
);

211 
	`printf
("Toggling monitoring...\n");

212 
	`kill
(
child_pid
, 
SIGUSR2
);

215 
	`printf
("Quitting and saving statistics...\n");

216 
	`cleanup
(0);

222 
	`waitpid
(
child_pid
, &
status
, 0);

223 
	`printf
("Monitoring complete.\n");

226 
	}
}

	@memtrace.c

1 #define 
	#_GNU_SOURCE


	)

2 #include 
	~<stdio.h
>

3 #include 
	~<stdlib.h
>

4 #include 
	~<dlfcn.h
>

5 #include 
	~<time.h
>

6 #include 
	~<pthread.h
>

7 #include 
	~<unistd.h
>

8 #include 
	~<signal.h
>

9 #include 
	~<string.h
>

10 #include 
	~<sys/time.h
>

11 #include 
	~<math.h
>

14 static void* (*
	greal_malloc
)(
	gsize_t
) = 
NULL
;

15 static void (*
real_free
)(void*) = 
NULL
;

18 #define 
	#NUM_BUCKETS
 30

19 typedef struct {

	)

20 
size_t
 
count
;

21 double 
total_time
;

22 } 
	tbucket_stats_t
;

25 static 
size_t
 
malloc_count
 = 0;

26 static 
size_t
 
free_count
 = 0;

27 static double 
total_malloc_time
 = 0.0;

28 static double 
total_free_time
 = 0.0;

29 static struct 
timespec
 
start_time
;

30 static int 
monitoring
 = 1;

31 static 
pthread_mutex_t
 
stats_mutex
 = 
PTHREAD_MUTEX_INITIALIZER
;

34 static 
bucket_stats_t
 
malloc_buckets
[
NUM_BUCKETS
] = {0
	}
};

35 static 
bucket_stats_t
 
	gfree_buckets
[
NUM_BUCKETS
] = {0};

38 #define 
	#POINTER_MAP_SIZE
 10000

	)

40 void* 
	mptr
;

41 
size_t
 
	msize
;

42 } 
	tptr_size_map_t
;

43 static 
ptr_size_map_t
 
	gpointer_map
[
POINTER_MAP_SIZE
] = {0};

44 static 
size_t
 
	gpointer_map_count
 = 0;

47 static double 
	$get_time_ns
() {

48 struct 
timespec
 
ts
;

49 
	`clock_gettime
(
CLOCK_MONOTONIC
, &
ts
);

50 return (double)
ts
.
tv_sec
 * 1e9 + (double)ts.
tv_nsec
;

51 
	}
}

54 static double 
	$elapsed_seconds
() {

55 struct 
timespec
 
now
;

56 
	`clock_gettime
(
CLOCK_MONOTONIC
, &
now
);

57 return (
now
.
tv_sec
 - 
start_time
.tv_sec) +

58 (
now
.
tv_nsec
 - 
start_time
.tv_nsec) / 1e9;

59 
	}
}

62 static int 
	$get_bucket_index
(
size_t
 
size
) {

63 if (
size
 == 0) return 0;

66 int 
highest_bit
 = 0;

67 
size_t
 
temp
 = 
size
;

68 while (
temp
 >>= 1) {

69 
highest_bit
++;

73 if (
size
 == (1UL << 
highest_bit
)) {

74 return 
highest_bit
;

78 return 
highest_bit
 + 1;

79 
	}
}

82 static void 
	$store_pointer
(void* 
ptr
, 
size_t
 
size
) {

83 
	`pthread_mutex_lock
(&
stats_mutex
);

86 
size_t
 
index
 = ((size_t)
ptr
 / sizeof(void*)) % 
POINTER_MAP_SIZE
;

89 while (
pointer_map
[
index
].
ptr
 != 
NULL
 && pointer_map[index].ptr != ptr) {

90 
index
 = (index + 1) % 
POINTER_MAP_SIZE
;

93 
pointer_map
[
index
].
ptr
 = ptr;

94 
pointer_map
[
index
].
size
 = size;

96 if (
pointer_map_count
 < 
POINTER_MAP_SIZE
) {

97 
pointer_map_count
++;

100 
	`pthread_mutex_unlock
(&
stats_mutex
);

101 
	}
}

104 static 
size_t
 
	$get_pointer_size
(void* 
ptr
) {

105 
size_t
 
size
 = 0;

107 
	`pthread_mutex_lock
(&
stats_mutex
);

110 
size_t
 
index
 = ((size_t)
ptr
 / sizeof(void*)) % 
POINTER_MAP_SIZE
;

111 
size_t
 
start_index
 = 
index
;

114 if (
pointer_map
[
index
].
ptr
 == ptr) {

115 
size
 = 
pointer_map
[
index
].size;

116 
pointer_map
[
index
].
ptr
 = 
NULL
;

117 
pointer_map
[
index
].
size
 = 0;

118 if (
pointer_map_count
 > 0) {

119 
pointer_map_count
--;

124 
index
 = (index + 1) % 
POINTER_MAP_SIZE
;

125 } while (
index
 != 
start_index
 && 
pointer_map
[index].
ptr
 != 
NULL
);

127 
	`pthread_mutex_unlock
(&
stats_mutex
);

129 return 
size
;

130 
	}
}

132 static void 
print_statistics
(
FILE
 *
output
);

134 static int 
	$save_statistics_to_file
() {

136 
time_t
 
now
 = 
	`time
(
NULL
);

137 struct 
tm
 *
tm_info
 = 
	`localtime
(&
now
);

139 char 
filename
[64];

140 
	`strftime
(
filename
, sizeof(filename), "malloc-stat-%H%M%S.txt", 
tm_info
);

142 
FILE
 *
file
 = 
	`fopen
(
filename
, "w");

143 if (
file
 == 
NULL
) {

144 
	`fprintf
(
stderr
, "Error: Could not create statistics file %s\n", 
filename
);

148 
	`fprintf
(
stderr
, "Saving statistics to %s\n", 
filename
);

149 
	`print_statistics
(
file
);

150 
	`fclose
(
file
);

152 
	}
}

155 static void 
	$signal_handler
(int 
sig
) {

156 if (
sig
 == 
SIGUSR1
) {

157 
	`print_statistics
(
stderr
);

158 } else if (
sig
 == 
SIGUSR2
) {

160 
	`pthread_mutex_lock
(&
stats_mutex
);

161 
monitoring
 = !monitoring;

162 
	`fprintf
(
stderr
, "\n=== Memory monitoring %s ===\n",

163 
monitoring
 ? "resumed" : "paused");

164 
	`pthread_mutex_unlock
(&
stats_mutex
);

165 } else if (
sig
 == 
SIGURG
) {

167 
	`save_statistics_to_file
();

169 
	}
}

172 static void 
	$print_statistics
(
FILE
 *
output
) {

173 double 
elapsed
 = 
	`elapsed_seconds
();

174 double 
malloc_per_sec
 = 
malloc_count
 / 
elapsed
;

175 double 
free_per_sec
 = 
free_count
 / 
elapsed
;

176 double 
avg_malloc_time
 = 
malloc_count
 > 0 ? 
total_malloc_time
 / malloc_count / 1e3 : 0;

177 double 
avg_free_time
 = 
free_count
 > 0 ? 
total_free_time
 / free_count / 1e3 : 0;

179 
	`fprintf
(
output
, "\n=== Memory Allocation Statistics ===\n");

180 
	`fprintf
(
output
, "Total malloc calls: %zu\n", 
malloc_count
);

181 
	`fprintf
(
output
, "Total free calls: %zu\n", 
free_count
);

182 
	`fprintf
(
output
, "Elapsed time: %.2f seconds\n", 
elapsed
);

183 
	`fprintf
(
output
, "Malloc calls per second: %.2f\n", 
malloc_per_sec
);

184 
	`fprintf
(
output
, "Free calls per second: %.2f\n", 
free_per_sec
);

185 
	`fprintf
(
output
, "Average malloc time: %.4f useconds\n", 
avg_malloc_time
);

186 
	`fprintf
(
output
, "Average free time: %.4f useconds\n", 
avg_free_time
);

189 
	`fprintf
(
output
, "\n=== Size-Based Statistics ===\n");

190 
	`fprintf
(
output
, "Size Range | Malloc Count | Avg Malloc Time (us) | Free Count | Avg Free Time (us)\n");

191 
	`fprintf
(
output
, "--------------- | ------------ | -------------------- | ---------- | ------------------\n");

193 for (int 
i
 = 0; i < 
NUM_BUCKETS
; i++) {

194 if (
malloc_buckets
[
i
].
count
 > 0 || 
free_buckets
[i].count > 0) {

195 
size_t
 
lower_bound
 = 
i
 == 0 ? 0 : (1UL << (i-1));

196 
size_t
 
upper_bound
 = 
i
 == 0 ? 1 : (1UL << i);

198 char 
size_range
[32];

199 if (
i
 == 0) {

200 
	`snprintf
(
size_range
, sizeof(size_range), "0 bytes");

201 } else if (
upper_bound
 >= 1024*1024) {

202 
	`snprintf
(
size_range
, sizeof(size_range), "%zu-%zu MB",

203 
lower_bound
/(1024*1024), 
upper_bound
/(1024*1024));

204 } else if (
upper_bound
 >= 1024) {

205 
	`snprintf
(
size_range
, sizeof(size_range), "%zu-%zu KB",

206 
lower_bound
/1024, 
upper_bound
/1024);

208 
	`snprintf
(
size_range
, sizeof(size_range), "%zu-%zu bytes",

209 
lower_bound
, 
upper_bound
);

212 double 
avg_malloc
 = 
malloc_buckets
[
i
].
count
 > 0 ?

213 
malloc_buckets
[
i
].
total_time
 / malloc_buckets[i].
count
 / 1e3 : 0;

215 double 
avg_free
 = 
free_buckets
[
i
].
count
 > 0 ?

216 
free_buckets
[
i
].
total_time
 / free_buckets[i].
count
 / 1e3 : 0;

218 
	`fprintf
(
output
, "%-15s | %12zu | %20.4f | %10zu | %18.4f\n",

219 
size_range
,

220 
malloc_buckets
[
i
].
count
,

221 
avg_malloc
,

222 
free_buckets
[
i
].
count
,

223 
avg_free
);

227 
	`fprintf
(
output
, "===================================\n");

228 
	}
}

233 
__attribute__
((
constructor
))

234 static void 
	$init
(void) {

236 
real_malloc
 = 
	`dlsym
(
RTLD_NEXT
, "malloc");

237 
real_free
 = 
	`dlsym
(
RTLD_NEXT
, "free");

239 if (!
real_malloc
 || !
real_free
) {

240 
	`fprintf
(
stderr
, "Error: Failed to get original malloc/free functions\n");

241 
	`exit
(1);

245 
	`signal
(
SIGUSR1
, 
signal_handler
);

246 
	`signal
(
SIGUSR2
, 
signal_handler
);

247 
	`signal
(
SIGURG
, 
signal_handler
);

250 
	`clock_gettime
(
CLOCK_MONOTONIC
, &
start_time
);

252 
	`fprintf
(
stderr
, "Memory allocation monitoring started (PID: %d)\n", 
	`getpid
());

253 
	`fprintf
(
stderr
, "Send SIGUSR1 to get statistics\n");

254 
	`fprintf
(
stderr
, "Send SIGUSR2 to toggle monitoring\n");

255 
	}
}

258 
__attribute__
((
destructor
))

259 static void 
	$cleanup
(void) {

260 
	`print_statistics
(
stderr
);

261 
	`save_statistics_to_file
();

262 
	}
}

265 void* 
	$malloc
(
size_t
 
size
) {

266 if (!
real_malloc
) {

267 
real_malloc
 = 
	`dlsym
(
RTLD_NEXT
, "malloc");

270 double 
start
, 
end
;

271 void* 
ptr
;

273 int 
should_monitor
 = 
monitoring
;

275 if (
should_monitor
) {

276 
start
 = 
	`get_time_ns
();

277 
ptr
 = 
	`real_malloc
(
size
);

278 
end
 = 
	`get_time_ns
();

280 
	`pthread_mutex_lock
(&
stats_mutex
);

281 
malloc_count
++;

282 
total_malloc_time
 += (
end
 - 
start
);

285 int 
bucket
 = 
	`get_bucket_index
(
size
);

286 if (
bucket
 < 
NUM_BUCKETS
) {

287 
malloc_buckets
[
bucket
].
count
++;

288 
malloc_buckets
[
bucket
].
total_time
 += (
end
 - 
start
);

290 
	`pthread_mutex_unlock
(&
stats_mutex
);

293 if (
ptr
 != 
NULL
) {

294 
	`store_pointer
(
ptr
, 
size
);

297 
ptr
 = 
	`real_malloc
(
size
);

300 return 
ptr
;

301 
	}
}

304 void 
	$free
(void* 
ptr
) {

305 if (!
real_free
) {

306 
real_free
 = 
	`dlsym
(
RTLD_NEXT
, "free");

309 if (
ptr
 == 
NULL
) {

310 
	`real_free
(
ptr
);

314 double 
start
, 
end
;

316 
	`pthread_mutex_lock
(&
stats_mutex
);

317 int 
should_monitor
 = 
monitoring
;

318 
	`pthread_mutex_unlock
(&
stats_mutex
);

320 if (
should_monitor
) {

322 
size_t
 
size
 = 
	`get_pointer_size
(
ptr
);

324 
start
 = 
	`get_time_ns
();

325 
	`real_free
(
ptr
);

326 
end
 = 
	`get_time_ns
();

328 
	`pthread_mutex_lock
(&
stats_mutex
);

329 
free_count
++;

330 
total_free_time
 += (
end
 - 
start
);

333 if (
size
 > 0) {

334 int 
bucket
 = 
	`get_bucket_index
(
size
);

335 if (
bucket
 < 
NUM_BUCKETS
) {

336 
free_buckets
[
bucket
].
count
++;

337 
free_buckets
[
bucket
].
total_time
 += (
end
 - 
start
);

340 
	`pthread_mutex_unlock
(&
stats_mutex
);

342 
	`real_free
(
ptr
);

344 
	}
}

	@test_alloc.c

1 #include 
	~<stdio.h
>

2 #include 
	~<stdlib.h
>

3 #include 
	~<unistd.h
>

4 #include 
	~<time.h
>

6 #define 
	#NUM_ALLOCS
 1000

	)

7 #define 
	#MAX_SIZE
 0x1000000

	)

8 #define 
	#SLEEP_INTERVAL_US
 1000

9 

	)

10 int 
	$main
(int 
argc
, char *
argv
[]) {

11 
	`printf
("Memory allocation test program\n");

12 
	`printf
("This program will perform random allocations and frees\n");

14 
	`srand
(
	`time
(
NULL
));

17 void *
ptrs
[
NUM_ALLOCS
] = {
NULL
};

20 for (int 
i
 = 0; i < 100000; i++) {

22 int 
idx
 = 
	`rand
() % 
NUM_ALLOCS
;

25 if (
ptrs
[
idx
] != 
NULL
) {

26 
	`free
(
ptrs
[
idx
]);

27 
ptrs
[
idx
] = 
NULL
;

31 
size_t
 
size
 = 
	`rand
() % 
MAX_SIZE
 + 1;

32 
ptrs
[
idx
] = 
	`malloc
(
size
);

35 if (
ptrs
[
idx
]) {

36 *((char*)
ptrs
[
idx
]) = 1;

40 
	`usleep
(
SLEEP_INTERVAL_US
);

43 if (
i
 % 1000 == 0) {

44 
	`printf
("Completed %d operations\r", 
i
);

45 
	`fflush
(
stdout
);

50 for (int 
i
 = 0; i < 
NUM_ALLOCS
; i++) {

51 if (
ptrs
[
i
] != 
NULL
) {

52 
	`free
(
ptrs
[
i
]);

56 
	`printf
("\nTest completed\n");

58 
	}
}

	@
1
.
0
6
73
.vscode/preinclude.h
iotest.c
iotrace.c
memmon.c
memtrace.c
test_alloc.c
