#define _GNU_SOURCE
#include <stdio.h>
#include <dlfcn.h>
#include <stdlib.h>
#include <pthread.h>
#include <time.h>
#include <signal.h>
#include <unistd.h>
#include <string.h>
#include "base_hooks.h"


// Initialize statistics for 1 functions

// Function pointers to original functions
static void *(*orig_malloc)(size_t __size) = NULL;

// Initialize library
__attribute__((constructor))
static void init(void) {
    orig_malloc = dlsym(RTLD_NEXT, "malloc");
    if (!orig_malloc) {
        fprintf(stderr, "Error: Failed to get original malloc function\n");
        exit(1);
    }

    set_fname(0, "malloc");
    set_maxfunc(1);
        
    static_init();

}


// Hook for malloc
void * malloc(size_t __size) {
    
    // Add your tracing code here
    struct timespec call_start_time;
    if (orig_malloc == NULL) {
        orig_malloc = dlsym(RTLD_NEXT, "malloc");
    }
    pre_call(0, &call_start_time);
    void *result = orig_malloc(__size);
    post_call(0, &call_start_time);
    return result;
}