#!/usr/bin/env python3

import os
import sys
import re
import argparse
from typing import List, Dict, Set

def find_function_prototype(func_name: str, include_dirs: List[str] = None, include_files: List[str] = None) -> str:
    """Search through include directories and files for function prototype."""
    found_prototypes = set()

    # Default include directories
    search_dirs = ['/usr/include']
    if include_dirs:
        search_dirs.extend(include_dirs)

    def search_file_for_prototype(filepath: str) -> None:
        """Helper function to search a file for function prototypes."""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                pattern = rf'\s*([a-zA-Z_]\w*\**)\s+\**\s*{func_name}(\s*\(([^)]*)\))\s*'
                matches = re.finditer(pattern, content)
                
                for match in matches:
                    print(f"Found prototype in {filepath}: {match.group(0)}")
                    prototype = match.group(0)
                    prototype = re.sub(r'\bextern\b', '', prototype).strip()
                    found_prototypes.add(prototype)
        except (IOError, UnicodeDecodeError):
            pass

    # First check include_files within include_dirs
    if include_files:
        # Try to find each include_file in the search directories
        for include_file in include_files:
            # If path is absolute or exists directly, try it first
            if os.path.isabs(include_file) or os.path.isfile(include_file):
                filepath = include_file
                if os.path.isfile(filepath):
                    search_file_for_prototype(filepath)
            
            # Then try to find the file in include directories
            for include_dir in search_dirs:
                filepath = os.path.join(include_dir, include_file)
                if os.path.isfile(filepath):
                    search_file_for_prototype(filepath)
    else:
    # Finally search all .h files in include directories
        for include_dir in search_dirs:
            for root, _, files in os.walk(include_dir):
                for file in files:
                    if not file.endswith('.h'):
                        continue
                    filepath = os.path.join(root, file)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            content = f.read()
                            
                            # Look for function prototype
                            pattern = rf'\s*([a-zA-Z_]\w*\**)\s+\**\s*{func_name}(\s*\(([^)]*)\))\s*'
                            matches = re.finditer(pattern, content)
                            
                            for match in matches:
                                print(f"Found prototype in {filepath}: {match.group(0)}")
                                # Extract the prototype
                                prototype = match.group(0)
                                # Remove any extern keyword and leading/trailing whitespace
                                prototype = re.sub(r'\bextern\b', '', prototype).strip()
                                found_prototypes.add(prototype)
                    except (IOError, UnicodeDecodeError):
                        continue
    
    return list(found_prototypes)[0] if found_prototypes else None

def generate_hook_code(prototypes: Dict[str, str]) -> str:
    """Generate C code for function hooks."""
    code = []
    
    # Add necessary headers
    code.append("""#define _GNU_SOURCE
#include <stdio.h>
#include <dlfcn.h>
#include <stdlib.h>
#include <pthread.h>
#include <time.h>
#include <signal.h>
#include <unistd.h>
#include <string.h>
#include "base_hooks.h"
""")

    prototype_count = len(prototypes)
    code.append(f"\n// Initialize statistics for {prototype_count} functions")

    # Add function pointer declarations
    code.append("\n// Function pointers to original functions")
    for func_name in prototypes:
        # Extract return type from prototype
        return_type = prototypes[func_name].split(func_name)[0].strip()
        # Extract arguments from prototype
        args = re.search(rf'{func_name}\s*\((.*?)\)', prototypes[func_name]).group(1)
        code.append(f'static {return_type}(*orig_{func_name})({args}) = NULL;')

    
    # Add initialization function
    code.append("""
// Initialize library
__attribute__((constructor))
static void init(void) {""")
    
    index = 0
    for func_name in prototypes:
        code.append(f"""    orig_{func_name} = dlsym(RTLD_NEXT, "{func_name}");
    if (!orig_{func_name}) {{
        fprintf(stderr, "Error: Failed to get original {func_name} function\\n");
        exit(1);
    }}""")
        code.append(f"""
    set_fname({index}, "{func_name}");
    set_maxfunc({prototype_count});
        """)
        index += 1

    code.append("    static_init();\n")
    code.append("}\n")


    # Add function implementations
    index = 0
    for func_name, prototype in prototypes.items():
        # Extract return type and arguments

        return_type = prototype.split(func_name)[0].strip()
        args_with_types = re.search(rf'{func_name}\s*\((.*?)\)', prototype).group(1)
        
        # Create argument list without types for function call
        args_without_types = []
        if args_with_types and args_with_types != 'void':
            for arg in args_with_types.split(','):
                arg = arg.strip()
                # Extract variable name (last word before any array brackets or end)
                var_name = re.search(r'(\w+)(?:\s*\[|\s*$)', arg)
                if var_name:
                    args_without_types.append(var_name.group(1))
                else:
                    # If we can't parse it, use the whole arg (better than nothing)
                    args_without_types.append(arg.split()[-1])
        
        args_call = ', '.join(args_without_types)
        
        # Generate function implementation
        if return_type == 'void':
            code.append(f"""
// Hook for {func_name}
{return_type} {func_name}({args_with_types}) {{
    
    // Add your tracing code here
    struct timespec call_start_time;
    if (orig_{func_name} == NULL) {{
        orig_{func_name} = dlsym(RTLD_NEXT, "{func_name}");
    }}
    pre_call({index}, &call_start_time);
    orig_{func_name}({args_call});
    post_call({index}, &call_start_time);
    return;
}}""")

        else:
            return_type = return_type.strip()
            code.append(f"""
// Hook for {func_name}
{return_type} {func_name}({args_with_types}) {{
    
    // Add your tracing code here
    struct timespec call_start_time;
    if (orig_{func_name} == NULL) {{
        orig_{func_name} = dlsym(RTLD_NEXT, "{func_name}");
    }}
    pre_call({index}, &call_start_time);
    {return_type}result = orig_{func_name}({args_call});
    post_call({index}, &call_start_time);
    return result;
}}""")
        index += 1
    
    return '\n'.join(code)

def main():
    parser = argparse.ArgumentParser(description='Generate C function hooks')
    parser.add_argument('functions', nargs='*', help='Function names to hook')
    parser.add_argument('-f', '--file', help='File containing function names (one per line)')
    parser.add_argument('-I', '--include', action='append', help='Additional include directories to search')
    parser.add_argument('-i', '--include-file', action='append', help='Specific header files to search')
    args = parser.parse_args()
    
    functions = set()
    
    # Collect function names from arguments
    if args.functions:
        functions.update(args.functions)
    
    # Collect function names from file
    if args.file:
        try:
            with open(args.file, 'r') as f:
                functions.update(line.strip() for line in f if line.strip())
        except IOError as e:
            print(f"Error reading file {args.file}: {e}", file=sys.stderr)
            sys.exit(1)
    
    if not functions:
        print("No functions specified. Use positional arguments or -f option.", file=sys.stderr)
        sys.exit(1)
    
    # Find prototypes
    prototypes = {}
    for func in functions:
        proto = find_function_prototype(func, args.include, args.include_file)
        if proto:
            prototypes[func] = proto
        else:
            print(f"Warning: Could not find prototype for function '{func}'", file=sys.stderr)
    
    if not prototypes:
        print("No function prototypes found.", file=sys.stderr)
        sys.exit(1)
    
    # Generate code
    code = generate_hook_code(prototypes)
    
    # Write to output file
    output_file = "generated_hooks.c"
    with open(output_file, 'w') as f:
        f.write(code)
    
    print(f"Generated hooks written to {output_file}")

if __name__ == '__main__':
    main()