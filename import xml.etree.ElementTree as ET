import xml.etree.ElementTree as ET
import sys

class GObject:
    def __init__(self, title, rect):
        self.title = title
        self.rect = rect

def parse_xml(filename):
    # 创建一个字典来存储按y值分组的对象
    y_groups = {}
    # 创建一个列表来存储y值
    yindex = []
    
    # 解析XML文件
    tree = ET.parse(filename)
    root = tree.getroot()
    
    # 遍历所有g元素
    for g in root.findall('.//g'):
        title = g.find('title').text if g.find('title') is not None else ""
        rect = g.find('rect')
        if rect is not None:
            y = float(rect.get('y'))
            # 创建GObject实例
            obj = GObject(title, rect)
            
            # 使用y值作为键
            y_key = f"y{int(y)}"
            if y_key not in y_groups:
                y_groups[y_key] = []
                yindex.append(y_key)
            
            y_groups[y_key].append(obj)
    
    # 按y值排序yindex
    yindex.sort(key=lambda x: int(x[1:]))  # 去掉'y'前缀后按数字排序
    
    return y_groups, yindex

def print_group_info(index, y_groups, yindex):
    if index < 0 or index >= len(yindex):
        print(f"Invalid index. Index should be between 0 and {len(yindex)-1}")
        return
    
    y_key = yindex[index]
    group = y_groups[y_key]
    
    print(f"Group {y_key}:")
    print(f"Total length: {len(group)}")
    print("First 10 titles:")
    for i, obj in enumerate(group[:10]):
        print(f"{i+1}. {obj.title}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python perf_parse.py <index>")
        return
    
    try:
        index = int(sys.argv[1])
        y_groups, yindex = parse_xml('perf_core_all.xml')
        print_group_info(index, y_groups, yindex)
    except ValueError:
        print("Index must be an integer")
    except FileNotFoundError:
        print("Could not find perf_core_all.xml")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    main()