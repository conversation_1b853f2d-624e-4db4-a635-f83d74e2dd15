#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <time.h>

// Function to create and write to a file
void test_file_operations(const char *filename, int num_writes) {
    printf("Testing file operations with %s...\n", filename);
    
    // Open a file for writing
    int fd = open(filename, O_WRONLY | O_CREAT | O_TRUNC, 0644);
    if (fd < 0) {
        perror("Failed to open file");
        return;
    }
    
    // Write to the file multiple times
    char buffer[1024];
    for (int i = 0; i < num_writes; i++) {
        snprintf(buffer, sizeof(buffer), "This is test write #%d to file %s\n", i+1, filename);
        ssize_t bytes = write(fd, buffer, strlen(buffer));
        if (bytes < 0) {
            perror("Failed to write to file");
            close(fd);
            return;
        }
        
        // Small delay to make operations visible in monitoring
        usleep(10000);  // 10ms
    }
    
    // Close the file
    close(fd);
    
    printf("Completed file operations with %s\n", filename);
}

// Function to create a socket and send data
void test_socket_operations(const char *ip, int port, int num_sends) {
    printf("Testing socket operations to %s:%d...\n", ip, port);
    
    // Create a socket
    int sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0) {
        perror("Failed to create socket");
        return;
    }
    
    // Set up the server address
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    
    if (inet_pton(AF_INET, ip, &server_addr.sin_addr) <= 0) {
        perror("Invalid address");
        close(sockfd);
        return;
    }
    
    // Test send
    char buffer[1024];
    for (int i = 0; i < num_sends; i++) {
        snprintf(buffer, sizeof(buffer), "Test send #%d", i+1);
        ssize_t bytes = send(sockfd, buffer, strlen(buffer), 0);
        if (bytes < 0) {
            perror("Failed to send data");
            close(sockfd);
            return;
        }
        
        usleep(10000);  // 10ms
    }
    
    // Test sendto
    for (int i = 0; i < num_sends; i++) {
        snprintf(buffer, sizeof(buffer), "Test sendto #%d", i+1);
        ssize_t bytes = sendto(sockfd, buffer, strlen(buffer), 0,
                              (struct sockaddr*)&server_addr, sizeof(server_addr));
        if (bytes < 0) {
            perror("Failed to sendto data");
            close(sockfd);
            return;
        }
        
        usleep(10000);  // 10ms
    }
    
    // Test sendmsg
    for (int i = 0; i < num_sends; i++) {
        struct msghdr msg;
        struct iovec iov;
        
        snprintf(buffer, sizeof(buffer), "Test sendmsg #%d", i+1);
        
        iov.iov_base = buffer;
        iov.iov_len = strlen(buffer);
        
        memset(&msg, 0, sizeof(msg));
        msg.msg_name = &server_addr;
        msg.msg_namelen = sizeof(server_addr);
        msg.msg_iov = &iov;
        msg.msg_iovlen = 1;
        
        ssize_t bytes = sendmsg(sockfd, &msg, 0);
        if (bytes < 0) {
            perror("Failed to sendmsg data");
            close(sockfd);
            return;
        }
        
        usleep(10000);  // 10ms
    }
    
    // Close the socket
    close(sockfd);
    
    printf("Completed socket operations\n");
}

// Function to test different file sizes
void test_different_sizes() {
    printf("Testing writes with different sizes...\n");
    
    // Open a file for writing
    int fd = open("size_test.dat", O_WRONLY | O_CREAT | O_TRUNC, 0644);
    if (fd < 0) {
        perror("Failed to open file");
        return;
    }
    
    // Test different buffer sizes (powers of 2)
    for (size_t size = 2; size <= 1024*1024*2; size *= 2) {
        char *buffer = malloc(size);
        if (!buffer) {
            perror("Failed to allocate buffer");
            close(fd);
            return;
        }
        
        // Fill buffer with some data
        memset(buffer, 'A' + (size % 26), size);
        
        printf("  Writing %zu bytes...\n", size);
        ssize_t bytes = write(fd, buffer, size);
        if (bytes < 0) {
            perror("Failed to write to file");
            free(buffer);
            close(fd);
            return;
        }
        
        free(buffer);
        usleep(50000);  // 50ms between different sizes
    }
    
    // Close the file
    close(fd);
    
    printf("Completed size tests\n");
}

// Function to test multiple file descriptors
void test_multiple_fds(int num_files) {
    printf("Testing operations with multiple file descriptors...\n");
    
    int *fds = malloc(num_files * sizeof(int));
    if (!fds) {
        perror("Failed to allocate FD array");
        return;
    }
    
    // Open multiple files
    for (int i = 0; i < num_files; i++) {
        char filename[64];
        snprintf(filename, sizeof(filename), "test_file_%d.txt", i);
        
        fds[i] = open(filename, O_WRONLY | O_CREAT | O_TRUNC, 0644);
        if (fds[i] < 0) {
            perror("Failed to open file");
            // Close already opened files
            for (int j = 0; j < i; j++) {
                close(fds[j]);
            }
            free(fds);
            return;
        }
    }
    
    // Write to each file
    char buffer[1024];
    for (int i = 0; i < num_files; i++) {
        snprintf(buffer, sizeof(buffer), "This is test data for file %d\n", i);
        write(fds[i], buffer, strlen(buffer));
        usleep(10000);  // 10ms
    }
    
    // Close all files
    for (int i = 0; i < num_files; i++) {
        close(fds[i]);
    }
    
    free(fds);
    printf("Completed multiple FD test\n");
}

int main(int argc, char *argv[]) {
    printf("I/O Monitoring Test Program\n");
    printf("---------------------------\n");
    
    // Test file operations
    test_file_operations("test_file.txt", 5);
    
    // Test socket operations (note: these will likely fail to connect,
    // but the socket calls will still be monitored)
    test_socket_operations("127.0.0.1", 12345, 3);
    
    // Test different write sizes
    test_different_sizes();
    
    // Test multiple file descriptors
    test_multiple_fds(5);
    
    printf("\nAll tests completed. Press Enter to exit...\n");
    getchar();
    
    return 0;
} 