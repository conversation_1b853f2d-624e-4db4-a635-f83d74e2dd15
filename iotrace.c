#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <dlfcn.h>
#include <time.h>
#include <pthread.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <fcntl.h>
#include <errno.h>

// Function pointers to the real functions
static int (*real_open)(const char *, int, ...) = NULL;
static int (*real_close)(int) = NULL;
static int (*real_socket)(int, int, int) = NULL;
static ssize_t (*real_write)(int, const void *, size_t) = NULL;
static ssize_t (*real_send)(int, const void *, size_t, int) = NULL;
static ssize_t (*real_sendto)(int, const void *, size_t, int, const struct sockaddr *, socklen_t) = NULL;
static ssize_t (*real_sendmsg)(int, const struct msghdr *, int) = NULL;

// Define maximum number of file descriptors to track
#define MAX_FD 1024

// Statistics structure for each file descriptor
typedef struct {
    int is_active;                // Whether this FD is currently open
    int is_socket;                // Whether this FD is a socket
    char path[256];               // Path or description for this FD
    size_t open_count;            // Number of times opened
    size_t close_count;           // Number of times closed
    size_t write_count;           // Number of write operations
    size_t send_count;            // Number of send operations
    size_t sendto_count;          // Number of sendto operations
    size_t sendmsg_count;         // Number of sendmsg operations
    size_t total_bytes_written;   // Total bytes written through all operations
    double total_write_time;      // Total time spent in write operations (ns)
    double total_send_time;       // Total time spent in send operations (ns)
} fd_stats_t;

// Global statistics
static fd_stats_t fd_stats[MAX_FD] = {0};
static size_t total_open_count = 0;
static size_t total_socket_count = 0;
static size_t total_close_count = 0;
static size_t total_write_count = 0;
static size_t total_send_count = 0;
static size_t total_sendto_count = 0;
static size_t total_sendmsg_count = 0;
static size_t total_bytes_written = 0;
static double total_open_time = 0.0;
static double total_close_time = 0.0;
static double total_socket_time = 0.0;
static double total_write_time = 0.0;
static double total_send_time = 0.0;

static struct timespec start_time;
static int monitoring = 1;
static pthread_mutex_t stats_mutex = PTHREAD_MUTEX_INITIALIZER;

// Get current time in nanoseconds
static double get_time_ns() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (double)ts.tv_sec * 1e9 + (double)ts.tv_nsec;
}

// Calculate elapsed time in seconds
static double elapsed_seconds() {
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    return (now.tv_sec - start_time.tv_sec) + 
           (now.tv_nsec - start_time.tv_nsec) / 1e9;
}

// Forward declaration
static void print_statistics(FILE *output);

// Save statistics to a file with timestamp
static int save_statistics_to_file() {
    // Get current time for filename
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    
    char filename[64];
    strftime(filename, sizeof(filename), "io-stat-%H%M%S.txt", tm_info);
    
    FILE *file = fopen(filename, "w");
    if (file == NULL) {
        fprintf(stderr, "Error: Could not create statistics file %s\n", filename);
        return -1;
    }
    
    fprintf(stderr, "Saving I/O statistics to %s\n", filename);
    print_statistics(file);
    fclose(file);
    return 0;
}

// Signal handler
static void signal_handler(int sig) {
    if (sig == SIGUSR1) {
        print_statistics(stderr);
    } else if (sig == SIGUSR2) {
        // Toggle monitoring
        pthread_mutex_lock(&stats_mutex);
        monitoring = !monitoring;
        fprintf(stderr, "\n=== I/O monitoring %s ===\n", 
                monitoring ? "resumed" : "paused");
        pthread_mutex_unlock(&stats_mutex);
    } else if (sig == SIGURG) {
        // Save statistics to file when requested
        save_statistics_to_file();
    }
}

// Function to print statistics to a specified output
static void print_statistics(FILE *output) {
    double elapsed = elapsed_seconds();
    
    fprintf(output, "\n=== I/O Operation Statistics ===\n");
    fprintf(output, "Elapsed time: %.2f seconds\n", elapsed);
    fprintf(output, "Total open calls: %zu\n", total_open_count);
    fprintf(output, "Total socket calls: %zu\n", total_socket_count);
    fprintf(output, "Total close calls: %zu\n", total_close_count);
    fprintf(output, "Total write calls: %zu\n", total_write_count);
    fprintf(output, "Total send calls: %zu\n", total_send_count);
    fprintf(output, "Total sendto calls: %zu\n", total_sendto_count);
    fprintf(output, "Total sendmsg calls: %zu\n", total_sendmsg_count);
    fprintf(output, "Total bytes written: %zu\n", total_bytes_written);
    
    double avg_open_time = total_open_count > 0 ? total_open_time / total_open_count / 1e3 : 0;
    double avg_close_time = total_close_count > 0 ? total_close_time / total_close_count / 1e3 : 0;
    double avg_socket_time = total_socket_count > 0 ? total_socket_time / total_socket_count / 1e3 : 0;
    double avg_write_time = total_write_count > 0 ? total_write_time / total_write_count / 1e3 : 0;
    double avg_send_time = (total_send_count + total_sendto_count + total_sendmsg_count) > 0 ? 
                          total_send_time / (total_send_count + total_sendto_count + total_sendmsg_count) / 1e3 : 0;
    
    fprintf(output, "Average open time: %.4f useconds\n", avg_open_time);
    fprintf(output, "Average close time: %.4f useconds\n", avg_close_time);
    fprintf(output, "Average socket time: %.4f useconds\n", avg_socket_time);
    fprintf(output, "Average write time: %.4f useconds\n", avg_write_time);
    fprintf(output, "Average send time: %.4f useconds\n", avg_send_time);
    
    // Print per-FD statistics
    fprintf(output, "\n=== Per File Descriptor Statistics ===\n");
    fprintf(output, "FD  | Type   | Path/Description                      | Open | Close | Write | Send | Bytes Written\n");
    fprintf(output, "----+--------+---------------------------------------+------+-------+-------+------+--------------\n");
    
    for (int fd = 0; fd < MAX_FD; fd++) {
        if (fd_stats[fd].open_count > 0 || fd_stats[fd].is_active) {
            // Calculate total send operations for this FD
            size_t total_sends = fd_stats[fd].send_count + 
                                fd_stats[fd].sendto_count + 
                                fd_stats[fd].sendmsg_count;
            
            fprintf(output, "%3d | %-6s | %-37s | %4zu | %5zu | %5zu | %4zu | %12zu\n",
                    fd,
                    fd_stats[fd].is_socket ? "Socket" : "File",
                    fd_stats[fd].path,
                    fd_stats[fd].open_count,
                    fd_stats[fd].close_count,
                    fd_stats[fd].write_count,
                    total_sends,
                    fd_stats[fd].total_bytes_written);
        }
    }
    
    fprintf(output, "===================================\n");
}

// Initialize the library
__attribute__((constructor))
static void init(void) {
    // Get the real function pointers
    real_open = dlsym(RTLD_NEXT, "open");
    real_close = dlsym(RTLD_NEXT, "close");
    real_socket = dlsym(RTLD_NEXT, "socket");
    real_write = dlsym(RTLD_NEXT, "write");
    real_send = dlsym(RTLD_NEXT, "send");
    real_sendto = dlsym(RTLD_NEXT, "sendto");
    real_sendmsg = dlsym(RTLD_NEXT, "sendmsg");
    
    if (!real_open || !real_close || !real_socket || !real_write || 
        !real_send || !real_sendto || !real_sendmsg) {
        fprintf(stderr, "Error: Failed to get original function pointers\n");
        exit(1);
    }
    
    // Set up signal handlers
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    signal(SIGURG, signal_handler);
    
    // Record start time
    clock_gettime(CLOCK_MONOTONIC, &start_time);
    
    fprintf(stderr, "I/O operation monitoring started (PID: %d)\n", getpid());
    fprintf(stderr, "Send SIGUSR1 to get statistics\n");
    fprintf(stderr, "Send SIGUSR2 to toggle monitoring\n");
    
    // Initialize standard file descriptors
    strcpy(fd_stats[0].path, "stdin");
    fd_stats[0].is_active = 1;
    strcpy(fd_stats[1].path, "stdout");
    fd_stats[1].is_active = 1;
    strcpy(fd_stats[2].path, "stderr");
    fd_stats[2].is_active = 1;
}

// Clean up
__attribute__((destructor))
static void cleanup(void) {
    print_statistics(stderr);
    save_statistics_to_file();
}

// Intercepted open
int open(const char *pathname, int flags, ...) {
    if (!real_open) {
        real_open = dlsym(RTLD_NEXT, "open");
    }
    
    mode_t mode = 0;
    if (flags & O_CREAT) {
        va_list args;
        va_start(args, flags);
        mode = va_arg(args, mode_t);
        va_end(args);
    }
    
    double start, end;
    int fd;
    
    int should_monitor = monitoring;
    
    if (should_monitor) {
        start = get_time_ns();
        fd = real_open(pathname, flags, mode);
        end = get_time_ns();
        
        if (fd >= 0 && fd < MAX_FD) {
            pthread_mutex_lock(&stats_mutex);
            total_open_count++;
            total_open_time += (end - start);
            
            fd_stats[fd].is_active = 1;
            fd_stats[fd].is_socket = 0;
            fd_stats[fd].open_count++;
            strncpy(fd_stats[fd].path, pathname, sizeof(fd_stats[fd].path) - 1);
            fd_stats[fd].path[sizeof(fd_stats[fd].path) - 1] = '\0';
            pthread_mutex_unlock(&stats_mutex);
        }
    } else {
        fd = real_open(pathname, flags, mode);
    }
    
    return fd;
}

// Intercepted socket
int socket(int domain, int type, int protocol) {
    if (!real_socket) {
        real_socket = dlsym(RTLD_NEXT, "socket");
    }
    
    double start, end;
    int fd;
    
    int should_monitor = monitoring;
    
    if (should_monitor) {
        start = get_time_ns();
        fd = real_socket(domain, type, protocol);
        end = get_time_ns();
        
        if (fd >= 0 && fd < MAX_FD) {
            pthread_mutex_lock(&stats_mutex);
            total_socket_count++;
            total_socket_time += (end - start);
            
            fd_stats[fd].is_active = 1;
            fd_stats[fd].is_socket = 1;
            fd_stats[fd].open_count++;
            
            // Create a description for the socket
            char desc[256];
            snprintf(desc, sizeof(desc), "socket(domain=%d, type=%d, protocol=%d)", 
                    domain, type, protocol);
            strncpy(fd_stats[fd].path, desc, sizeof(fd_stats[fd].path) - 1);
            fd_stats[fd].path[sizeof(fd_stats[fd].path) - 1] = '\0';
            pthread_mutex_unlock(&stats_mutex);
        }
    } else {
        fd = real_socket(domain, type, protocol);
    }
    
    return fd;
}

// Intercepted close
int close(int fd) {
    if (!real_close) {
        real_close = dlsym(RTLD_NEXT, "close");
    }
    
    double start, end;
    int result;
    
    int should_monitor = monitoring;
    
    if (should_monitor && fd >= 0 && fd < MAX_FD) {
        start = get_time_ns();
        result = real_close(fd);
        end = get_time_ns();
        
        pthread_mutex_lock(&stats_mutex);
        total_close_count++;
        total_close_time += (end - start);
        
        if (result == 0) {  // Only mark as inactive if close was successful
            fd_stats[fd].is_active = 0;
            fd_stats[fd].close_count++;
        }
        pthread_mutex_unlock(&stats_mutex);
    } else {
        result = real_close(fd);
    }
    
    return result;
}

// Intercepted write
ssize_t write(int fd, const void *buf, size_t count) {
    if (!real_write) {
        real_write = dlsym(RTLD_NEXT, "write");
    }
    
    double start, end;
    ssize_t result;
    
    int should_monitor = monitoring;
    
    if (should_monitor && fd >= 0 && fd < MAX_FD) {
        start = get_time_ns();
        result = real_write(fd, buf, count);
        end = get_time_ns();
        
        if (result > 0) {
            pthread_mutex_lock(&stats_mutex);
            total_write_count++;
            total_write_time += (end - start);
            total_bytes_written += result;
            
            fd_stats[fd].write_count++;
            fd_stats[fd].total_bytes_written += result;
            fd_stats[fd].total_write_time += (end - start);
            pthread_mutex_unlock(&stats_mutex);
        }
    } else {
        result = real_write(fd, buf, count);
    }
    
    return result;
}

// Intercepted send
ssize_t send(int sockfd, const void *buf, size_t len, int flags) {
    if (!real_send) {
        real_send = dlsym(RTLD_NEXT, "send");
    }
    
    double start, end;
    ssize_t result;
    
    int should_monitor = monitoring;
    
    if (should_monitor && sockfd >= 0 && sockfd < MAX_FD) {
        start = get_time_ns();
        result = real_send(sockfd, buf, len, flags);
        end = get_time_ns();
        
        if (result > 0) {
            pthread_mutex_lock(&stats_mutex);
            total_send_count++;
            total_send_time += (end - start);
            total_bytes_written += result;
            
            fd_stats[sockfd].send_count++;
            fd_stats[sockfd].total_bytes_written += result;
            fd_stats[sockfd].total_send_time += (end - start);
            pthread_mutex_unlock(&stats_mutex);
        }
    } else {
        result = real_send(sockfd, buf, len, flags);
    }
    
    return result;
}

// Intercepted sendto
ssize_t sendto(int sockfd, const void *buf, size_t len, int flags,
               const struct sockaddr *dest_addr, socklen_t addrlen) {
    if (!real_sendto) {
        real_sendto = dlsym(RTLD_NEXT, "sendto");
    }
    
    double start, end;
    ssize_t result;
    
    int should_monitor = monitoring;
    
    if (should_monitor && sockfd >= 0 && sockfd < MAX_FD) {
        start = get_time_ns();
        result = real_sendto(sockfd, buf, len, flags, dest_addr, addrlen);
        end = get_time_ns();
        
        if (result > 0) {
            pthread_mutex_lock(&stats_mutex);
            total_sendto_count++;
            total_send_time += (end - start);
            total_bytes_written += result;
            
            fd_stats[sockfd].sendto_count++;
            fd_stats[sockfd].total_bytes_written += result;
            fd_stats[sockfd].total_send_time += (end - start);
            pthread_mutex_unlock(&stats_mutex);
        }
    } else {
        result = real_sendto(sockfd, buf, len, flags, dest_addr, addrlen);
    }
    
    return result;
}

// Intercepted sendmsg
ssize_t sendmsg(int sockfd, const struct msghdr *msg, int flags) {
    if (!real_sendmsg) {
        real_sendmsg = dlsym(RTLD_NEXT, "sendmsg");
    }
    
    double start, end;
    ssize_t result;
    
    int should_monitor = monitoring;
    
    if (should_monitor && sockfd >= 0 && sockfd < MAX_FD) {
        start = get_time_ns();
        result = real_sendmsg(sockfd, msg, flags);
        end = get_time_ns();
        
        if (result > 0) {
            pthread_mutex_lock(&stats_mutex);
            total_sendmsg_count++;
            total_send_time += (end - start);
            total_bytes_written += result;
            
            fd_stats[sockfd].sendmsg_count++;
            fd_stats[sockfd].total_bytes_written += result;
            fd_stats[sockfd].total_send_time += (end - start);
            pthread_mutex_unlock(&stats_mutex);
        }
    } else {
        result = real_sendmsg(sockfd, msg, flags);
    }
    
    return result;
} 