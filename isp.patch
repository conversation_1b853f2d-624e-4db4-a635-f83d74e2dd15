commit 0b5e56cb7aa815630214269fa688f3d369d87bef
Author: s<PERSON><PERSON>.cheng <<EMAIL>>
Date:   Thu Jun 12 21:31:13 2025 +0800

    feat(isp): [J6X-5255] Fix qnx mutex scope/kthread stop name fault/ls asymmetry issure
    
    Change-Id: Icb8c6a2b395d05ebc35b29bc78c8e9e7ac13f99f

diff --git a/isp/hb_isp/src/isp_buffer.c b/isp/hb_isp/src/isp_buffer.c
index 281cc99f..7e31d252 100755
--- a/isp/hb_isp/src/isp_buffer.c
+++ b/isp/hb_isp/src/isp_buffer.c
@@ -367,8 +367,8 @@ static void isp_frame_done_output(uint32_t hw_id, uint32_t slot_id, uint32_t blo
 		}
 		vio_e_barrier_irqs(framemgr, flags);
 		trans_frame(framemgr, vframe, FS_COMPLETE);
-		vio_x_barrier_irqr(framemgr, flags);
 		vctx->event = VIO_FRAME_DONE;
+		vio_x_barrier_irqr(framemgr, flags);
 		osal_wake_up_poll(&vctx->done_wq);
 
 		if (vdev->next != NULL) {
diff --git a/isp/isp_base/isp/fsm/sbuf/sbuf_func.c b/isp/isp_base/isp/fsm/sbuf/sbuf_func.c
index 477d77ef..fb6bb640 100755
--- a/isp/isp_base/isp/fsm/sbuf/sbuf_func.c
+++ b/isp/isp_base/isp/fsm/sbuf/sbuf_func.c
@@ -1135,7 +1135,7 @@ static int sbuf_fops_release(struct inode *inode, struct file *f)
 		LOG(LOG_ERR, "[hw%d][slot%d]Error: lock failed.", p_ctx->hw_id, p_ctx->slot_id);
 		return rc;
 	}
-
+#ifdef __linux__
 	if ((p_ctx->dev_opened) && (NULL != p_ctx->sbuf_mgr.sbuf_base)) {
 		p_ctx->dev_opened = 0;
 		f->private_data = NULL;
@@ -1148,11 +1148,31 @@ static int sbuf_fops_release(struct inode *inode, struct file *f)
 		LOG(LOG_CRIT, "[hw%d][slot%d]Fatal error: wrong state dev_opened: %d.", p_ctx->hw_id, p_ctx->slot_id, p_ctx->dev_opened);
 		rc = -EINVAL;
 	}
+#elif defined(__QNX__)
+	if (p_ctx->dev_opened) {
+		p_ctx->dev_opened = 0;
+		f->private_data = NULL;
+		/* note  terminated SIGSEGV need to resolve  mummap p_ctx->sbuf_mgr.sbuf_base */
+		// p_ctx->sbuf_mgr.sbuf_base->kf_info.cali_info.is_fetched = 0; // user-app will need this to
+		// read calibrations next time it runs
+		if (NULL != p_ctx->sbuf_mgr.sbuf_base) {
+			sbuf_mgr_reset(&p_ctx->sbuf_mgr);
+		}
+		LOG(LOG_DEBUG, "sbuf closed p_ctx: %pK, name: %s, slot_id: %d, minor_id: %d.",
+				p_ctx, p_ctx->dev_name, p_ctx->slot_id, p_ctx->dev_minor_id);
+	} else {
+		LOG(LOG_CRIT, "[hw%d][slot%d]Fatal error: wrong state dev_opened: %d.", p_ctx->hw_id, p_ctx->slot_id, p_ctx->dev_opened);
+		if (p_ctx->dev_opened)
+			p_ctx->dev_opened = 0;
+		rc = -EINVAL;
+	}
+#endif
 
 	osal_mutex_unlock(&p_ctx->fops_lock);
 
 	return 0;
 }
+
 /**
  * @NO{S08E02C01}
  * @ASIL{B}
diff --git a/isp/isp_base/platform/system_interrupts.c b/isp/isp_base/platform/system_interrupts.c
index 3a015956..f5aaff22 100755
--- a/isp/isp_base/platform/system_interrupts.c
+++ b/isp/isp_base/platform/system_interrupts.c
@@ -352,6 +352,7 @@ static irqreturn_t system_interrupt_handler(int irq, void *dev_id)
 	    isp_work_item_node->stWork.data.regs.mcfe, isp_work_item_node->stWork.data.regs.sof, isp_work_item_node->stWork.data.regs.eof);
 
 #ifdef __QNX__
+#ifdef ISP_STL
 	if (isp_work_item_node->stWork.data.regs.mcfe == 0u &&
 			isp_work_item_node->stWork.data.regs.sof == 0u &&
 			isp_work_item_node->stWork.data.regs.eof == 0u) {
@@ -360,6 +361,7 @@ static irqreturn_t system_interrupt_handler(int irq, void *dev_id)
 				isp_work_item_node->stWork.data.slot);
 		(void)diag_send_event_software(isp_stl_get_module_id(p_hw_ctx->hw_id), (uint16_t)EventId_IrqMismatch, (uint8_t)NCF_LEVEL, (uint8_t)EventStaFail);
 	}
+#endif
 #endif
 	update_fe_start_frame_id(p_hw_ctx->hw_id, isp_work_item_node, isp_work_item_node->stWork.data.slot, isp_work_item_node->stWork.data.regs);
 	interlock_check_manual_sched(p_hw_ctx->hw_id);
@@ -421,10 +423,12 @@ static irqreturn_t system_interrupt_fusa_handler(int irq, void *dev_id)
 	stats = acamera_frontend_interrupts_stats_status_read(phy_addr_isp(p_hw_ctx->hw_id)) & ACAMERA_FRONTEND_INTERRUPTS_FAULT_COLLECTOR_STATUS_FIELD_MASK;
 
 #ifdef __QNX__
+#ifdef ISP_STL
 	if (stats == 0u) {
 		LOG(LOG_ERR, "[hw%d][slot%d]Invalid fusa interrupt", p_hw_ctx->hw_id, slot_id);
 		(void)diag_send_event_software(isp_stl_get_module_id(p_hw_ctx->hw_id), (uint16_t)EventId_IrqMismatch, (uint8_t)NCF_LEVEL, (uint8_t)EventStaFail);
 	}
+#endif
 #endif
 
 	/* step-3: clear interrupt ISP fusa stats registers.*/
diff --git a/isp/isp_base/src/init.c b/isp/isp_base/src/init.c
index d9ebddb4..edcabeb5 100755
--- a/isp/isp_base/src/init.c
+++ b/isp/isp_base/src/init.c
@@ -69,14 +69,19 @@ extern acamera_settings settings[ISP_IP_MAX][FIRMWARE_CONTEXT_NUMBER];
 static int connection_thread(void *foo)
 {
 	isp_hw_ctx_t *p_hw_ctx = (isp_hw_ctx_t *)foo;
+
 #ifdef __QNX__
+	isp_fw_ctx_t *p_fw_ctx = isp_drv_get_fw_ctx();
 	//delete after verified
 	ThreadCtl( _NTO_TCTL_IO_LEVEL, (void*)_NTO_IO_LEVEL_1 );
 #endif
 	LOG(LOG_INFO, "[hw%d]Connection_thread start", p_hw_ctx->hw_id);
 	acamera_connection_init(p_hw_ctx->hw_id);
-
-    while ( !osal_thread_should_stop(&p_hw_ctx->isp_fw_process_thread) ) {
+#ifdef __linux__
+	while ( !osal_thread_should_stop(&p_hw_ctx->isp_fw_process_thread) ) {
+#elif defined(__QNX__)
+	while ( !osal_thread_should_stop(&p_fw_ctx->isp_fw_connections_thread) ) {
+#endif
 		acamera_connection_process(p_hw_ctx->hw_id);
 #if ISP_HAS_CONNECTION_BUFFER
 		system_timer_usleep(ISP_CONNECTION_PERIOD_MS * 1000);
@@ -608,7 +613,6 @@ int32_t isp_platform_probe(struct platform_device *pdev)
 #endif
 
 	platform_set_drvdata(pdev, (void *)&p_fw_ctx->hw_ctx[hw_id]);
-
 	LOG(LOG_NOTICE, "[hw%d]isp_platform_probe done: pdev = %pK\n", hw_id, pdev);
 	return rc;
 
