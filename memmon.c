#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <libgen.h>
#include <limits.h>
#include <errno.h>
#include <termios.h>
#include <fcntl.h>

pid_t child_pid = -1;

// Function to handle cleanup
void cleanup(int signum) {
    if (child_pid > 0) {
        printf("Terminating monitored process...\n");
        
        // Send SIGUSR1 to get final statistics and save to file
        kill(child_pid, SIGUSR1);
        
        // Send a special signal to indicate we want to save to file
        // We'll use SIGURG (urgent data signal) as it's rarely used
        kill(child_pid, SIGURG);
        
        // Give the process a moment to print and save statistics
        usleep(200000);  // 200ms
        
        // Then terminate it
        kill(child_pid, SIGTERM);
    }
    exit(0);
}

// Function to get a character without waiting for Enter
int getch() {
    struct termios old_tio, new_tio;
    int ch;
    
    // Get the terminal settings
    tcgetattr(STDIN_FILENO, &old_tio);
    
    // Copy the old settings
    new_tio = old_tio;
    
    // Disable canonical mode and echo
    new_tio.c_lflag &= ~(ICANON | ECHO);
    
    // Apply the new settings
    tcsetattr(STDIN_FILENO, TCSANOW, &new_tio);
    
    // Read a character
    ch = getchar();
    
    // Restore the old settings
    tcsetattr(STDIN_FILENO, TCSANOW, &old_tio);
    
    return ch;
}

int main(int argc, char *argv[]) {
    // Check if a program was specified
    if (argc < 2) {
        printf("Usage: %s <program> [program arguments...]\n", argv[0]);
        return 1;
    }
    
    // Get the current directory using a more portable approach
    char path[PATH_MAX];
    char *dir;
    
    // Use the program's argv[0] to determine the path
    if (argv[0][0] == '/') {
        // Absolute path was used to invoke the program
        strncpy(path, argv[0], PATH_MAX - 1);
        path[PATH_MAX - 1] = '\0';
    } else if (strchr(argv[0], '/') != NULL) {
        // Relative path was used
        char cwd[PATH_MAX];
        if (getcwd(cwd, PATH_MAX) == NULL) {
            perror("Failed to get current working directory");
            return 1;
        }
        snprintf(path, PATH_MAX, "%s/%s", cwd, argv[0]);
    } else {
        // Program was found in PATH
        char *path_env = getenv("PATH");
        if (path_env == NULL) {
            fprintf(stderr, "PATH environment variable not set\n");
            return 1;
        }
        
        // Make a copy of PATH as strtok modifies the string
        char *path_copy = strdup(path_env);
        if (path_copy == NULL) {
            perror("Memory allocation failed");
            return 1;
        }
        
        char *dir_path = strtok(path_copy, ":");
        int found = 0;
        
        while (dir_path != NULL) {
            snprintf(path, PATH_MAX, "%s/%s", dir_path, argv[0]);
            if (access(path, X_OK) == 0) {
                found = 1;
                break;
            }
            dir_path = strtok(NULL, ":");
        }
        
        free(path_copy);
        
        if (!found) {
            // If not found in PATH, use current directory as fallback
            if (getcwd(path, PATH_MAX) == NULL) {
                perror("Failed to get current working directory");
                return 1;
            }
        }
    }
    
    // Extract directory from path
    dir = dirname(path);
    
    // Compile the memory tracing library if needed
    char memtrace_path[PATH_MAX];
    snprintf(memtrace_path, PATH_MAX, "%s/memtrace.so", dir);
    
    // Check if memtrace.so exists
    struct stat memtrace_stat;
    
    if (stat(memtrace_path, &memtrace_stat) == -1) {
        printf("memtrace.so does not exist\n");
        exit(1);
    }
    
    // Set up environment for the child process
    char ld_preload[PATH_MAX * 2];
    snprintf(ld_preload, PATH_MAX * 2, "LD_PRELOAD=%s", memtrace_path);
    
    // Create a new environment with LD_PRELOAD
    char **new_env = malloc(sizeof(char *) * 2);
    if (!new_env) {
        perror("Memory allocation failed");
        return 1;
    }
    new_env[0] = ld_preload;
    new_env[1] = NULL;
    
    // Fork and exec the target program
    printf("Starting program with memory monitoring...\n");
    
    child_pid = fork();
    if (child_pid == -1) {
        perror("Fork failed");
        free(new_env);
        return 1;
    }
    
    if (child_pid == 0) {
        // Child process
        execve(argv[1], &argv[1], new_env);
        
        // If execve returns, it failed
        perror("execve failed");
        exit(1);
    }
    
    // Parent process
    free(new_env);
    
    // Set up signal handlers
    signal(SIGINT, cleanup);
    signal(SIGTERM, cleanup);
    
    // Display menu
    printf("Memory monitoring active for PID: %d\n", child_pid);
    printf("Commands:\n");
    printf("  s - Show current statistics\n");
    printf("  p - Pause/resume monitoring\n");
    printf("  q - Quit and show final statistics\n");
    
    // Command loop
    int status;
    pid_t wait_result;
    int cmd;
    
    while (1) {
        // Check if the child has exited
        wait_result = waitpid(child_pid, &status, WNOHANG);
        if (wait_result == child_pid) {
            printf("Monitored process has terminated.\n");
            break;
        } else if (wait_result == -1) {
            perror("waitpid failed");
            break;
        }
        
        // Get command
        cmd = getch();
        
        switch (cmd) {
            case 's':
                printf("Requesting statistics...\n");
                kill(child_pid, SIGUSR1);
                break;
            case 'p':
                printf("Toggling monitoring...\n");
                kill(child_pid, SIGUSR2);
                break;
            case 'q':
                printf("Quitting and saving statistics...\n");
                cleanup(0);
                break;
        }
    }
    
    // Wait for the child to exit
    waitpid(child_pid, &status, 0);
    printf("Monitoring complete.\n");
    
    return 0;
} 