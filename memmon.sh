#!/bin/bash

# Memory allocation monitoring script

# Check if a program was specified
if [ $# -lt 1 ]; then
    echo "Usage: $0 <program> [program arguments...]"
    exit 1
fi

# Compile the memory tracing library if needed
if [ ! -f "memtrace.so" ] || [ "memtrace.c" -nt "memtrace.so" ]; then
    echo "Compiling memory tracing library..."
    gcc -shared -fPIC -o memtrace.so memtrace.c -ldl -pthread
    if [ $? -ne 0 ]; then
        echo "Failed to compile memtrace.so"
        exit 1
    fi
fi

# Get the current directory
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Run the program with our preloaded library
echo "Starting program with memory monitoring..."
LD_PRELOAD="$DIR/memtrace.so" "$@" &
PID=$!

# Function to handle cleanup
cleanup() {
    if kill -0 $PID 2>/dev/null; then
        echo "Terminating monitored process..."
        kill $PID
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Display menu
echo "Memory monitoring active for PID: $PID"
echo "Commands:"
echo "  s - Show current statistics"
echo "  p - Pause/resume monitoring"
echo "  q - Quit and show final statistics"

# Command loop
while true; do
    read -n 1 -s cmd
    case "$cmd" in
        s)
            echo "Requesting statistics..."
            kill -SIGUSR1 $PID
            ;;
        p)
            echo "Toggling monitoring..."
            kill -SIGUSR2 $PID
            ;;
        q)
            cleanup
            ;;
    esac
    
    # Check if the monitored process is still running
    if ! kill -0 $PID 2>/dev/null; then
        echo "Monitored process has terminated."
        break
    fi
done

wait $PID
echo "Monitoring complete." 