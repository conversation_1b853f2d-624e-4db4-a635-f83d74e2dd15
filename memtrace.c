#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <dlfcn.h>
#include <time.h>
#include <pthread.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include <sys/time.h>
#include <math.h>

// Function pointers to the real malloc and free
static void* (*real_malloc)(size_t) = NULL;
static void (*real_free)(void*) = NULL;

// Define size buckets (powers of 2)
#define NUM_BUCKETS 30  // Covers sizes from 1 byte to over 1GB
typedef struct {
    size_t count;
    double total_time;
} bucket_stats_t;

// Statistics
static size_t malloc_count = 0;
static size_t free_count = 0;
static double total_malloc_time = 0.0;
static double total_free_time = 0.0;
static struct timespec start_time;
static int monitoring = 1;
static pthread_mutex_t stats_mutex = PTHREAD_MUTEX_INITIALIZER;

// Size-based statistics
static bucket_stats_t malloc_buckets[NUM_BUCKETS] = {0};
static bucket_stats_t free_buckets[NUM_BUCKETS] = {0};

// Pointer to size mapping for tracking free operations
#define POINTER_MAP_SIZE 10000
typedef struct {
    void* ptr;
    size_t size;
} ptr_size_map_t;
static ptr_size_map_t pointer_map[POINTER_MAP_SIZE] = {0};
static size_t pointer_map_count = 0;

// Get current time in nanoseconds
static double get_time_ns() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (double)ts.tv_sec * 1e9 + (double)ts.tv_nsec;
}

// Calculate elapsed time in seconds
static double elapsed_seconds() {
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    return (now.tv_sec - start_time.tv_sec) + 
           (now.tv_nsec - start_time.tv_nsec) / 1e9;
}

// Determine which bucket a size belongs to
static int get_bucket_index(size_t size) {
    if (size == 0) return 0;
    
    // Find the highest bit set (log2 ceiling)
    int highest_bit = 0;
    size_t temp = size;
    while (temp >>= 1) {
        highest_bit++;
    }
    
    // If size is exactly a power of 2, use that bucket
    if (size == (1UL << highest_bit)) {
        return highest_bit;
    }
    
    // Otherwise, use the next power of 2 bucket
    return highest_bit + 1;
}

// Store pointer and its size
static void store_pointer(void* ptr, size_t size) {
    pthread_mutex_lock(&stats_mutex);
    
    // Simple hash-based storage - in a real app you'd want a proper hash table
    size_t index = ((size_t)ptr / sizeof(void*)) % POINTER_MAP_SIZE;
    
    // Linear probing to find an empty slot or the same pointer
    while (pointer_map[index].ptr != NULL && pointer_map[index].ptr != ptr) {
        index = (index + 1) % POINTER_MAP_SIZE;
    }
    
    pointer_map[index].ptr = ptr;
    pointer_map[index].size = size;
    
    if (pointer_map_count < POINTER_MAP_SIZE) {
        pointer_map_count++;
    }
    
    pthread_mutex_unlock(&stats_mutex);
}

// Get size for a pointer and remove it from the map
static size_t get_pointer_size(void* ptr) {
    size_t size = 0;
    
    pthread_mutex_lock(&stats_mutex);
    
    // Find the pointer in our map
    size_t index = ((size_t)ptr / sizeof(void*)) % POINTER_MAP_SIZE;
    size_t start_index = index;
    
    do {
        if (pointer_map[index].ptr == ptr) {
            size = pointer_map[index].size;
            pointer_map[index].ptr = NULL;
            pointer_map[index].size = 0;
            if (pointer_map_count > 0) {
                pointer_map_count--;
            }
            break;
        }
        
        index = (index + 1) % POINTER_MAP_SIZE;
    } while (index != start_index && pointer_map[index].ptr != NULL);
    
    pthread_mutex_unlock(&stats_mutex);
    
    return size;
}

static void print_statistics(FILE *output);

static int save_statistics_to_file() {
    // Get current time for filename
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    
    char filename[64];
    strftime(filename, sizeof(filename), "malloc-stat-%H%M%S.txt", tm_info);
    
    FILE *file = fopen(filename, "w");
    if (file == NULL) {
        fprintf(stderr, "Error: Could not create statistics file %s\n", filename);
        return -1;
    }
    
    fprintf(stderr, "Saving statistics to %s\n", filename);
    print_statistics(file);
    fclose(file);
    return 0;
}

// Signal handler for SIGUSR1, SIGUSR2, and SIGURG
static void signal_handler(int sig) {
    if (sig == SIGUSR1) {
        print_statistics(stderr);
    } else if (sig == SIGUSR2) {
        // Toggle monitoring
        pthread_mutex_lock(&stats_mutex);
        monitoring = !monitoring;
        fprintf(stderr, "\n=== Memory monitoring %s ===\n", 
                monitoring ? "resumed" : "paused");
        pthread_mutex_unlock(&stats_mutex);
    } else if (sig == SIGURG) {
        // Save statistics to file when requested (e.g., on Ctrl-C)
        save_statistics_to_file();
    }
}

// Function to print statistics to a specified output
static void print_statistics(FILE *output) {
    double elapsed = elapsed_seconds();
    double malloc_per_sec = malloc_count / elapsed;
    double free_per_sec = free_count / elapsed;
    double avg_malloc_time = malloc_count > 0 ? total_malloc_time / malloc_count / 1e3 : 0;
    double avg_free_time = free_count > 0 ? total_free_time / free_count / 1e3 : 0;
    
    fprintf(output, "\n=== Memory Allocation Statistics ===\n");
    fprintf(output, "Total malloc calls: %zu\n", malloc_count);
    fprintf(output, "Total free calls: %zu\n", free_count);
    fprintf(output, "Elapsed time: %.2f seconds\n", elapsed);
    fprintf(output, "Malloc calls per second: %.2f\n", malloc_per_sec);
    fprintf(output, "Free calls per second: %.2f\n", free_per_sec);
    fprintf(output, "Average malloc time: %.4f useconds\n", avg_malloc_time);
    fprintf(output, "Average free time: %.4f useconds\n", avg_free_time);
    
    // Print size-based statistics
    fprintf(output, "\n=== Size-Based Statistics ===\n");
    fprintf(output, "Size Range      | Malloc Count | Avg Malloc Time (us) | Free Count | Avg Free Time (us)\n");
    fprintf(output, "--------------- | ------------ | -------------------- | ---------- | ------------------\n");
    
    for (int i = 0; i < NUM_BUCKETS; i++) {
        if (malloc_buckets[i].count > 0 || free_buckets[i].count > 0) {
            size_t lower_bound = i == 0 ? 0 : (1UL << (i-1));
            size_t upper_bound = i == 0 ? 1 : (1UL << i);
            
            char size_range[32];
            if (i == 0) {
                snprintf(size_range, sizeof(size_range), "0 bytes");
            } else if (upper_bound >= 1024*1024) {
                snprintf(size_range, sizeof(size_range), "%zu-%zu MB", 
                         lower_bound/(1024*1024), upper_bound/(1024*1024));
            } else if (upper_bound >= 1024) {
                snprintf(size_range, sizeof(size_range), "%zu-%zu KB", 
                         lower_bound/1024, upper_bound/1024);
            } else {
                snprintf(size_range, sizeof(size_range), "%zu-%zu bytes", 
                         lower_bound, upper_bound);
            }
            
            double avg_malloc = malloc_buckets[i].count > 0 ? 
                malloc_buckets[i].total_time / malloc_buckets[i].count / 1e3 : 0;
            
            double avg_free = free_buckets[i].count > 0 ? 
                free_buckets[i].total_time / free_buckets[i].count / 1e3 : 0;
            
            fprintf(output, "%-15s | %12zu | %20.4f | %10zu | %18.4f\n",
                    size_range, 
                    malloc_buckets[i].count, 
                    avg_malloc,
                    free_buckets[i].count, 
                    avg_free);
        }
    }
    
    fprintf(output, "===================================\n");
}

// Function to save statistics to a file with timestamp

// Initialize the library
__attribute__((constructor))
static void init(void) {
    // Get the real malloc and free functions
    real_malloc = dlsym(RTLD_NEXT, "malloc");
    real_free = dlsym(RTLD_NEXT, "free");
    
    if (!real_malloc || !real_free) {
        fprintf(stderr, "Error: Failed to get original malloc/free functions\n");
        exit(1);
    }
    
    // Set up signal handlers
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    signal(SIGURG, signal_handler);  // Add handler for SIGURG
    
    // Record start time
    clock_gettime(CLOCK_MONOTONIC, &start_time);
    
    fprintf(stderr, "Memory allocation monitoring started (PID: %d)\n", getpid());
    fprintf(stderr, "Send SIGUSR1 to get statistics\n");
    fprintf(stderr, "Send SIGUSR2 to toggle monitoring\n");
}

// Clean up
__attribute__((destructor))
static void cleanup(void) {
    print_statistics(stderr); // Print final statistics to stderr
    save_statistics_to_file(); // Also save to file
}

// Intercepted malloc
void* malloc(size_t size) {
    if (!real_malloc) {
        real_malloc = dlsym(RTLD_NEXT, "malloc");
    }
    
    double start, end;
    void* ptr;
    
    int should_monitor = monitoring;
    
    if (should_monitor) {
        start = get_time_ns();
        ptr = real_malloc(size);
        end = get_time_ns();
        
        pthread_mutex_lock(&stats_mutex);
        malloc_count++;
        total_malloc_time += (end - start);
        
        // Update size-based statistics
        int bucket = get_bucket_index(size);
        if (bucket < NUM_BUCKETS) {
            malloc_buckets[bucket].count++;
            malloc_buckets[bucket].total_time += (end - start);
        }
        pthread_mutex_unlock(&stats_mutex);
        
        // Store pointer and size for later free tracking
        if (ptr != NULL) {
            store_pointer(ptr, size);
        }
    } else {
        ptr = real_malloc(size);
    }
    
    return ptr;
}

// Intercepted free
void free(void* ptr) {
    if (!real_free) {
        real_free = dlsym(RTLD_NEXT, "free");
    }
    
    if (ptr == NULL) {
        real_free(ptr);
        return;
    }
    
    double start, end;
    
    pthread_mutex_lock(&stats_mutex);
    int should_monitor = monitoring;
    pthread_mutex_unlock(&stats_mutex);
    
    if (should_monitor) {
        // Get the size of this pointer before freeing it
        size_t size = get_pointer_size(ptr);
        
        start = get_time_ns();
        real_free(ptr);
        end = get_time_ns();
        
        pthread_mutex_lock(&stats_mutex);
        free_count++;
        total_free_time += (end - start);
        
        // Update size-based statistics if we know the size
        if (size > 0) {
            int bucket = get_bucket_index(size);
            if (bucket < NUM_BUCKETS) {
                free_buckets[bucket].count++;
                free_buckets[bucket].total_time += (end - start);
            }
        }
        pthread_mutex_unlock(&stats_mutex);
    } else {
        real_free(ptr);
    }
}