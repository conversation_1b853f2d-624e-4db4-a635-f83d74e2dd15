import xml.etree.ElementTree as ET
import sys

class GObject:
    def __init__(self, title, x, y, w, h):
        self.title = title
        self.x = x
        self.y = y
        self.w = w
        self.h = h

def parse_xml(filename):
    # 创建一个字典来存储按y值分组的对象
    y_groups = {}
    # 创建一个列表来存储y值
    yindex = []
    
    # 解析XML文件
    tree = ET.parse(filename)
    root = tree.getroot()
    print("根元素:", root.tag)
    for child in root:
        print(f"子元素: {child.tag}, 属性: {child.attrib}")
    
    # Define namespace for easier reference
    ns = {'svg': 'http://www.w3.org/2000/svg'}
    
    # Find the frames group element
    frames_group = root.find(".//svg:g[@id='frames']", ns)
    
    # Iterate through all g elements within the frames group
    if frames_group is not None:
        for g in frames_group.findall("svg:g", ns):
            title_elem = g.find("svg:title", ns)
            rect_elem = g.find("svg:rect", ns)
            
            if title_elem is not None and rect_elem is not None:
                title = title_elem.text
                x = float(rect_elem.get('x'))
                y = int(rect_elem.get('y'))
                w = float(rect_elem.get('width'))
                h = float(rect_elem.get('height'))
                
                #print(f"Found object: {title}, x: {x}, y: {y}, w: {w}, h: {h}")
                # 创建GObject实例
                obj = GObject(title, x, y, w, h)
                
                # 使用y值作为键
                y_key = f"y{int(y)}"
                if y_key not in y_groups:
                    y_groups[y_key] = []
                    yindex.append(y_key)
                
                y_groups[y_key].append(obj)
    
    # 按y值排序yindex
    yindex.sort(key=lambda x: int(x[1:]))  # 去掉'y'前缀后按数字排序
    
    return y_groups, yindex

def print_group_info(index, y_groups, yindex):
    if index < 0 or index >= len(yindex):
        print(f"Invalid index. Index should be between 0 and {len(yindex)-1}")
        return
    
    y_key = yindex[index]
    group = y_groups[y_key]
    
    print(f"Group {y_key}:  ", end="")
    print(f"Total length: {len(group)} ", end="")
    print("First 10 titles:")
    for i, obj in enumerate(group[:10]):
        print(f"{i+1}. {obj.title}")

def main():
    if len(sys.argv) != 2:
        print("Usage: python perf_parse.py <index>")
        return
    
    try:
        index = int(sys.argv[1])
        y_groups, yindex = parse_xml('perf_core_all.xml')
        for i in range(len(yindex)):
            print_group_info(i, y_groups, yindex)
        print("total groups: ", len(yindex))
    except ValueError:
        print("Index must be an integer")
    except FileNotFoundError:
        print("Could not find perf_core_all.xml")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    main()
