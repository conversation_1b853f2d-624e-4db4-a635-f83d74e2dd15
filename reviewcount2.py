#!/usr/bin/python3
# Copyright (C) 2025

import pandas as pd
import sys
import os
import re

def main():
    if len(sys.argv) < 3:
        print("Usage: python reviewcount.py <input.xlsx> <output.xlsx>")
        sys.exit(1)
    infile = sys.argv[1]
    outfile = sys.argv[2]
    df = pd.read_excel(infile)
    # Extract Year and Month from '创建时间'
    df['Year'] = pd.to_datetime(df['创建时间']).dt.year
    df['Month'] = pd.to_datetime(df['创建时间']).dt.month
    
    # 先过滤出 Year=2025 的表格，存在 fdf 中，后续操作都基于 fdf
    fdf = df[df['Year'] == 2025].copy()
    # Add 'Self Class' column
    def extract_self_class(comment):
        if not isinstance(comment, str):
            return 'A'
        for cls in ['A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D']:
            if f'[{cls}]' in comment:
                return cls
        return 'A'
    fdf['Self Class'] = fdf['评论内容'].astype(str).apply(extract_self_class)
    # Add 'Self Class Value' column
    class_value_map = {'A': 1, 'A+': 2, 'B': 4, 'B+': 6, 'C': 10, 'C+': 15, 'D': 20, 'D+': 25}
    fdf['Value'] = fdf['Self Class'].map(class_value_map).fillna(1)

    filter_comments = [
        'force',
        'need reset',
        'need rebuild',
        'need_rebuild',
        'run cicd',
        'run unittest',
        'need unittest',
        'Ci-Lint reset',
        'run j6xota',
        'run j6x_ota',
        'run qnx_ota',
        'build ok',
        'send rebuild',
        'force rebuild',
        'need build',
        'done'
        # 可以在这里添加更多需要过滤的内容
    ]
    filter_comments2 = [
        '> Patch Set'
        # 可以在这里添加更多需要前缀过滤的内容
    ]
    if '评论内容' in fdf.columns:
        col = fdf['评论内容'].astype(str)
        col_stripped = col.str.strip()
        mask_exact = ~col_stripped.isin(filter_comments)
        mask_prefix = ~col_stripped.str.startswith(tuple(filter_comments2))
        mask = mask_exact & mask_prefix
        filtered_df = fdf[~mask]  # rows to filter out
        kept_df = fdf[mask]       # rows to keep
        # Write filtered rows to another file
        base, ext = os.path.splitext(outfile)
        filter_outfile = f"{base}_filter{ext}"
        filtered_df.to_excel(filter_outfile, index=False)
        #kept_df.to_excel(outfile, index=False)
    else:
        fdf.to_excel(outfile, index=False)
    # 统计输出：按‘评论作者’分组，统计‘Value’之和，Year=2025, Month=3,4,5，从大到小排列，并写入outfile的第二个sheet
    if all(col in kept_df.columns for col in ['Year', 'Month', '评论作者', 'Value']):
        stat_df = kept_df[kept_df['Month'].isin([3, 4, 5])]
        stat = stat_df.groupby('评论作者', as_index=False)['Value'].sum()
        stat = stat.rename(columns={'Value': 'ValueSum'})
        stat = stat.sort_values('ValueSum', ascending=False)
        # 写入outfile的第二个sheet
        with pd.ExcelWriter(outfile, engine='openpyxl', mode='w' if os.path.exists(outfile) else 'w') as writer:
            kept_df.to_excel(writer, index=False, sheet_name='Sheet1')
            stat.to_excel(writer, index=False, sheet_name='ValueStat_2025_3-5')

if __name__ == '__main__':
    main()