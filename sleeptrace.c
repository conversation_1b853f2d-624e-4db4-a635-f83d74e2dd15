#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <dlfcn.h>
#include <time.h>
#include <pthread.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include <sys/time.h>

// Function pointers to the real sleep functions
static unsigned int (*real_sleep)(unsigned int seconds) = NULL;
static int (*real_usleep)(useconds_t usec) = NULL;
static int (*real_nanosleep)(const struct timespec *req, struct timespec *rem) = NULL;

// Statistics
static size_t sleep_count = 0;
static size_t usleep_count = 0;
static size_t nanosleep_count = 0;
static double total_sleep_time = 0.0;  // in seconds
static struct timespec start_time;
static int monitoring = 1;
static pthread_mutex_t stats_mutex = PTHREAD_MUTEX_INITIALIZER;

// Define time buckets (in nanoseconds)
#define NUM_BUCKETS 10
typedef struct {
    size_t count;
    double total_time;  // in seconds
} bucket_stats_t;

static bucket_stats_t sleep_buckets[NUM_BUCKETS] = {0};  // For tracking different sleep durations

// Get current time in nanoseconds
static double get_time_ns() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (double)ts.tv_sec * 1e9 + (double)ts.tv_nsec;
}

// Calculate elapsed time in seconds
static double elapsed_seconds() {
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    return (now.tv_sec - start_time.tv_sec) + 
           (now.tv_nsec - start_time.tv_nsec) / 1e9;
}

// Determine which bucket a sleep duration belongs to
static int get_bucket_index(double duration_ns) {
    if (duration_ns <= 0) return 0;
    
    // Buckets are organized as:
    // 0: 0-1us
    // 1: 1us-10us
    // 2: 10us-100us
    // 3: 100us-1ms
    // 4: 1ms-10ms
    // 5: 10ms-100ms
    // 6: 100ms-1s
    // 7: 1s-10s
    // 8: 10s-100s
    // 9: >100s
    
    double duration_us = duration_ns / 1000.0;
    if (duration_us <= 1) return 0;
    if (duration_us <= 10) return 1;
    if (duration_us <= 100) return 2;
    if (duration_us <= 1000) return 3;
    if (duration_us <= 10000) return 4;
    if (duration_us <= 100000) return 5;
    if (duration_us <= 1000000) return 6;
    if (duration_us <= 10000000) return 7;
    if (duration_us <= 100000000) return 8;
    return 9;
}

static void print_statistics(FILE *output);

static int save_statistics_to_file() {
    // Get current time for filename
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    
    char filename[64];
    strftime(filename, sizeof(filename), "sleep-stat-%H%M%S.txt", tm_info);
    
    FILE *file = fopen(filename, "w");
    if (file == NULL) {
        fprintf(stderr, "Error: Could not create statistics file %s\n", filename);
        return -1;
    }
    
    fprintf(stderr, "Saving statistics to %s\n", filename);
    print_statistics(file);
    fclose(file);
    return 0;
}

// Signal handler for SIGUSR1, SIGUSR2, and SIGURG
static void signal_handler(int sig) {
    if (sig == SIGUSR1) {
        print_statistics(stderr);
    } else if (sig == SIGUSR2) {
        // Toggle monitoring
        pthread_mutex_lock(&stats_mutex);
        monitoring = !monitoring;
        fprintf(stderr, "\n=== Sleep monitoring %s ===\n", 
                monitoring ? "resumed" : "paused");
        pthread_mutex_unlock(&stats_mutex);
    } else if (sig == SIGURG) {
        save_statistics_to_file();
    } else if (sig == SIGINT) {
        // Clean up and exit on Ctrl-C
        exit(0);
    }
}

// Function to print statistics
static void print_statistics(FILE *output) {
    double elapsed = elapsed_seconds();
    
    fprintf(output, "\n=== Sleep Function Statistics ===\n");
    fprintf(output, "Total sleep() calls: %zu\n", sleep_count);
    fprintf(output, "Total usleep() calls: %zu\n", usleep_count);
    fprintf(output, "Total nanosleep() calls: %zu\n", nanosleep_count);
    fprintf(output, "Total time spent sleeping: %.2f seconds\n", total_sleep_time);
    fprintf(output, "Elapsed time: %.2f seconds\n", elapsed);
    fprintf(output, "Average calls per second: %.2f\n", 
            (sleep_count + usleep_count + nanosleep_count) / elapsed);
    
    // Print duration-based statistics
    fprintf(output, "\n=== Duration-Based Statistics ===\n");
    fprintf(output, "Duration Range | Call Count | Total Time (s)\n");
    fprintf(output, "-------------- | ---------- | --------------\n");
    
    const char* ranges[] = {
        "0-1μs", "1-10μs", "10-100μs", "100μs-1ms", "1-10ms",
        "10-100ms", "100ms-1s", "1-10s", "10-100s", ">100s"
    };
    
    for (int i = 0; i < NUM_BUCKETS; i++) {
        if (sleep_buckets[i].count > 0) {
            fprintf(output, "%-14s | %10zu | %14.2f\n",
                    ranges[i],
                    sleep_buckets[i].count,
                    sleep_buckets[i].total_time);
        }
    }
    
    fprintf(output, "===================================\n");
}

// Initialize the library
__attribute__((constructor))
static void init(void) {
    // Get the real sleep functions
    real_sleep = dlsym(RTLD_NEXT, "sleep");
    real_usleep = dlsym(RTLD_NEXT, "usleep");
    real_nanosleep = dlsym(RTLD_NEXT, "nanosleep");
    
    if (!real_sleep || !real_usleep || !real_nanosleep) {
        fprintf(stderr, "Error: Failed to get original sleep functions\n");
        exit(1);
    }
    
    // Set up signal handlers
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    signal(SIGURG, signal_handler);
    signal(SIGINT, signal_handler);
    
    // Record start time
    clock_gettime(CLOCK_MONOTONIC, &start_time);
    
    fprintf(stderr, "Sleep function monitoring started (PID: %d)\n", getpid());
    fprintf(stderr, "Send SIGUSR1 to get statistics\n");
    fprintf(stderr, "Send SIGUSR2 to toggle monitoring\n");
}

// Clean up
__attribute__((destructor))
static void cleanup(void) {
    print_statistics(stderr);
    save_statistics_to_file();
}

// Intercepted sleep
unsigned int sleep(unsigned int seconds) {
    if (!real_sleep) {
        real_sleep = dlsym(RTLD_NEXT, "sleep");
    }
    
    double start_time = 0;
    unsigned int ret;
    
    pthread_mutex_lock(&stats_mutex);
    int should_monitor = monitoring;
    pthread_mutex_unlock(&stats_mutex);
    
    if (should_monitor) {
        start_time = get_time_ns();
        ret = real_sleep(seconds);
        double end_time = get_time_ns();
        double duration = (end_time - start_time) / 1e9;  // Convert to seconds
        
        pthread_mutex_lock(&stats_mutex);
        sleep_count++;
        total_sleep_time += duration;
        
        int bucket = get_bucket_index(seconds * 1e9);
        if (bucket < NUM_BUCKETS) {
            sleep_buckets[bucket].count++;
            sleep_buckets[bucket].total_time += duration;
        }
        pthread_mutex_unlock(&stats_mutex);
    } else {
        ret = real_sleep(seconds);
    }
    
    return ret;
}

// Intercepted usleep
int usleep(useconds_t usec) {
    if (!real_usleep) {
        real_usleep = dlsym(RTLD_NEXT, "usleep");
    }
    
    double start_time = 0;
    int ret;
    
    pthread_mutex_lock(&stats_mutex);
    int should_monitor = monitoring;
    pthread_mutex_unlock(&stats_mutex);
    
    if (should_monitor) {
        start_time = get_time_ns();
        ret = real_usleep(usec);
        double end_time = get_time_ns();
        double duration = (end_time - start_time) / 1e9;  // Convert to seconds
        
        pthread_mutex_lock(&stats_mutex);
        usleep_count++;
        total_sleep_time += duration;
        
        int bucket = get_bucket_index(usec * 1000.0);  // Convert usec to nsec
        if (bucket < NUM_BUCKETS) {
            sleep_buckets[bucket].count++;
            sleep_buckets[bucket].total_time += duration;
        }
        pthread_mutex_unlock(&stats_mutex);
    } else {
        ret = real_usleep(usec);
    }
    
    return ret;
}

// Intercepted nanosleep
int nanosleep(const struct timespec *req, struct timespec *rem) {
    if (!real_nanosleep) {
        real_nanosleep = dlsym(RTLD_NEXT, "nanosleep");
    }
    
    double start_time = 0;
    int ret;
    
    pthread_mutex_lock(&stats_mutex);
    int should_monitor = monitoring;
    pthread_mutex_unlock(&stats_mutex);
    
    if (should_monitor) {
        start_time = get_time_ns();
        ret = real_nanosleep(req, rem);
        double end_time = get_time_ns();
        double duration = (end_time - start_time) / 1e9;  // Convert to seconds
        
        pthread_mutex_lock(&stats_mutex);
        nanosleep_count++;
        total_sleep_time += duration;
        
        double req_ns = req->tv_sec * 1e9 + req->tv_nsec;
        int bucket = get_bucket_index(req_ns);
        if (bucket < NUM_BUCKETS) {
            sleep_buckets[bucket].count++;
            sleep_buckets[bucket].total_time += duration;
        }
        pthread_mutex_unlock(&stats_mutex);
    } else {
        ret = real_nanosleep(req, rem);
    }
    
    return ret;
}
