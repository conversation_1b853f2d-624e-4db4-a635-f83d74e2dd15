[{"size": 1, "isCallStack": false, "isCalling": false, "description": "", "items": [{"name": "perf_core_all.xml+41678", "kind": 20, "detail": "", "uri": "file:///perf_core_all.xml", "range": {"start": {"line": 41677, "character": 16}, "end": {"line": 41677, "character": 16}}, "selectionRange": {"start": {"line": 41677, "character": 16}, "end": {"line": 41677, "character": 16}}}]}, {"size": 1, "isCallStack": false, "isCalling": false, "description": "", "items": [{"name": "perf_core_all.xml+2478", "kind": 20, "detail": "", "uri": "file:///perf_core_all.xml", "range": {"start": {"line": 2477, "character": 11}, "end": {"line": 2477, "character": 11}}, "selectionRange": {"start": {"line": 2477, "character": 11}, "end": {"line": 2477, "character": 11}}}]}, {"size": 1, "description": "", "items": [{"name": "size_t", "kind": 20, "detail": "", "uri": "file:///memtrace.c", "head": false, "range": {"start": {"line": 14, "character": 0}, "end": {"line": 14, "character": 0}}, "selectionRange": {"start": {"line": 14, "character": 0}, "end": {"line": 14, "character": 0}}}]}, {"size": 3, "isCallStack": true, "isCalling": true, "description": "", "items": [{"name": "print_statistics", "kind": 11, "detail": "121;\"", "uri": "file:///base_hooks.c", "head": false, "range": {"start": {"line": 45, "character": 42}, "end": {"line": 45, "character": 52}}, "selectionRange": {"start": {"line": 120, "character": 0}, "end": {"line": 120, "character": 0}}}, {"name": "print_statistics", "kind": 11, "detail": "172;\"", "uri": "file:///memtrace.c", "head": false, "range": {"start": {"line": 171, "character": 0}, "end": {"line": 171, "character": 0}}, "selectionRange": {"start": {"line": 171, "character": 0}, "end": {"line": 171, "character": 0}}}, {"name": "total_time", "kind": 7, "detail": "21;\"", "uri": "file:///memtrace.c", "head": false, "range": {"start": {"line": 20, "character": 0}, "end": {"line": 20, "character": 0}}, "selectionRange": {"start": {"line": 20, "character": 0}, "end": {"line": 20, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "count", "kind": 7, "detail": "20;\"", "uri": "file:///memtrace.c", "head": false, "range": {"start": {"line": 19, "character": 0}, "end": {"line": 19, "character": 0}}, "selectionRange": {"start": {"line": 19, "character": 0}, "end": {"line": 19, "character": 0}}}]}, {"size": 2, "description": "", "items": [{"name": "stats_mutex", "kind": 12, "detail": "62;\"", "uri": "file:///iotrace.c", "head": false, "range": {"start": {"line": 61, "character": 0}, "end": {"line": 61, "character": 0}}, "selectionRange": {"start": {"line": 61, "character": 0}, "end": {"line": 61, "character": 0}}}, {"name": "stats_mutex", "kind": 12, "detail": "31;\"", "uri": "file:///memtrace.c", "head": false, "range": {"start": {"line": 30, "character": 0}, "end": {"line": 30, "character": 0}}, "selectionRange": {"start": {"line": 30, "character": 0}, "end": {"line": 30, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "total_time", "kind": 7, "detail": "21;\"", "uri": "file:///memtrace.c", "head": false, "range": {"start": {"line": 20, "character": 0}, "end": {"line": 20, "character": 0}}, "selectionRange": {"start": {"line": 20, "character": 0}, "end": {"line": 20, "character": 0}}}]}, {"size": 2, "description": "", "items": [{"name": "start_time", "kind": 12, "detail": "60;\"", "uri": "file:///iotrace.c", "head": false, "range": {"start": {"line": 59, "character": 0}, "end": {"line": 59, "character": 0}}, "selectionRange": {"start": {"line": 59, "character": 0}, "end": {"line": 59, "character": 0}}}, {"name": "start_time", "kind": 12, "detail": "29;\"", "uri": "file:///memtrace.c", "head": false, "range": {"start": {"line": 28, "character": 0}, "end": {"line": 28, "character": 0}}, "selectionRange": {"start": {"line": 28, "character": 0}, "end": {"line": 28, "character": 0}}}]}, {"size": 1, "description": "", "items": [{"name": "malloc", "kind": 11, "detail": "265;\"", "uri": "file:///memtrace.c", "head": false, "range": {"start": {"line": 264, "character": 0}, "end": {"line": 264, "character": 0}}, "selectionRange": {"start": {"line": 264, "character": 0}, "end": {"line": 264, "character": 0}}}]}, {"size": 3, "description": "", "items": [{"name": "main", "kind": 11, "detail": "", "uri": "file:///iotest.c", "head": false, "range": {"start": {"line": 212, "character": 0}, "end": {"line": 212, "character": 0}}, "selectionRange": {"start": {"line": 212, "character": 0}, "end": {"line": 212, "character": 0}}}, {"name": "main", "kind": 11, "detail": "", "uri": "file:///memmon.c", "head": false, "range": {"start": {"line": 62, "character": 0}, "end": {"line": 62, "character": 0}}, "selectionRange": {"start": {"line": 62, "character": 0}, "end": {"line": 62, "character": 0}}}, {"name": "main", "kind": 11, "detail": "", "uri": "file:///test_alloc.c", "head": false, "range": {"start": {"line": 9, "character": 0}, "end": {"line": 9, "character": 0}}, "selectionRange": {"start": {"line": 9, "character": 0}, "end": {"line": 9, "character": 0}}}]}]