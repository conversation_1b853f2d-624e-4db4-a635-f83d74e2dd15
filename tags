!_TAG_FILE_FORMAT	2	/extended format; --format=1 will not append ;" to lines/
!_TAG_FILE_SORTED	1	/0=unsorted, 1=sorted, 2=foldcase/
!_TAG_PROGRAM_AUTHOR	<PERSON>	/<EMAIL>/
!_TAG_PROGRAM_NAME	Exuberant Ctags	//
!_TAG_PROGRAM_URL	http://ctags.sourceforge.net	/official site/
!_TAG_PROGRAM_VERSION	5.9~svn20110310	//
ARTIFACT	Makefile	14;"	m
ARTIFACT_NAME_exe	Makefile	12;"	m
ARTIFACT_NAME_shared	Makefile	6;"	m
ARTIFACT_NAME_static	Makefile	9;"	m
ARTIFACT_TYPE	Makefile	2;"	m
BUILD_PROFILE	Makefile	20;"	m
CC	Makefile	28;"	m
CLEAN_DIRS	Makefile	101;"	m
CLEAN_FILES	Makefile	103;"	m
CLEAN_PATTERNS	Makefile	102;"	m
CONFIG_NAME	Makefile	22;"	m
CXX	Makefile	29;"	m
DEPS	Makefile	57;"	m
GCC	Makefile	31;"	m
LD	Makefile	33;"	m
LDFLAGS_exe	Makefile	11;"	m
LDFLAGS_shared	Makefile	5;"	m
LDFLAGS_static	Makefile	8;"	m
MAX_FD	iotrace.c	26;"	d	file:
MAX_SIZE	test_alloc.c	7;"	d	file:
MEMMON	Makefile	80;"	m
MMSO	Makefile	79;"	m
NUM_ALLOCS	test_alloc.c	6;"	d	file:
NUM_BUCKETS	memtrace.c	18;"	d	file:
OBJS	Makefile	66;"	m
OUTPUT_DIR	Makefile	23;"	m
PLATFORM	Makefile	17;"	m
POINTER_MAP_SIZE	memtrace.c	38;"	d	file:
PROJECT_NAME	Makefile	3;"	m
SLEEP_INTERVAL_US	test_alloc.c	8;"	d	file:
SRCS	Makefile	63;"	m
TARGET	Makefile	24;"	m
TEST	Makefile	78;"	m
_GNU_SOURCE	iotrace.c	1;"	d	file:
_GNU_SOURCE	memtrace.c	1;"	d	file:
__linux__	.vscode/preinclude.h	1;"	d
bucket_stats_t	memtrace.c	22;"	t	typeref:struct:__anon1	file:
child_pid	memmon.c	14;"	v
cleanup	iotrace.c	216;"	f	file:
cleanup	memmon.c	17;"	f
cleanup	memmon.sh	30;"	f
cleanup	memtrace.c	259;"	f	file:
close	iotrace.c	305;"	f
close_count	iotrace.c	34;"	m	struct:__anon3	file:
count	memtrace.c	20;"	m	struct:__anon1	file:
elapsed_seconds	iotrace.c	72;"	f	file:
elapsed_seconds	memtrace.c	54;"	f	file:
fd_stats	iotrace.c	45;"	v	file:
fd_stats_t	iotrace.c	42;"	t	typeref:struct:__anon3	file:
free	memtrace.c	304;"	f
free_buckets	memtrace.c	35;"	v	file:
free_count	memtrace.c	26;"	v	file:
get_bucket_index	memtrace.c	62;"	f	file:
get_pointer_size	memtrace.c	104;"	f	file:
get_time_ns	iotrace.c	65;"	f	file:
get_time_ns	memtrace.c	47;"	f	file:
getch	memmon.c	38;"	f
init	iotrace.c	177;"	f	file:
init	memtrace.c	234;"	f	file:
is_active	iotrace.c	30;"	m	struct:__anon3	file:
is_socket	iotrace.c	31;"	m	struct:__anon3	file:
main	iotest.c	213;"	f
main	memmon.c	63;"	f
main	test_alloc.c	10;"	f
malloc	memtrace.c	265;"	f
malloc_buckets	memtrace.c	34;"	v	file:
malloc_count	memtrace.c	25;"	v	file:
monitoring	iotrace.c	61;"	v	file:
monitoring	memtrace.c	30;"	v	file:
open	iotrace.c	222;"	f
open_count	iotrace.c	33;"	m	struct:__anon3	file:
path	iotrace.c	32;"	m	struct:__anon3	file:
pointer_map	memtrace.c	43;"	v	file:
pointer_map_count	memtrace.c	44;"	v	file:
print_statistics	iotrace.c	121;"	f	file:
print_statistics	memtrace.c	172;"	f	file:
ptr	memtrace.c	40;"	m	struct:__anon2	file:
ptr_size_map_t	memtrace.c	42;"	t	typeref:struct:__anon2	file:
real_close	iotrace.c	18;"	v	file:
real_free	memtrace.c	15;"	v	file:
real_malloc	memtrace.c	14;"	v	file:
real_open	iotrace.c	17;"	v	file:
real_send	iotrace.c	21;"	v	file:
real_sendmsg	iotrace.c	23;"	v	file:
real_sendto	iotrace.c	22;"	v	file:
real_socket	iotrace.c	19;"	v	file:
real_write	iotrace.c	20;"	v	file:
rwildcard	Makefile	60;"	m
save_statistics_to_file	iotrace.c	83;"	f	file:
save_statistics_to_file	memtrace.c	134;"	f	file:
send	iotrace.c	371;"	f
send_count	iotrace.c	36;"	m	struct:__anon3	file:
sendmsg	iotrace.c	440;"	f
sendmsg_count	iotrace.c	38;"	m	struct:__anon3	file:
sendto	iotrace.c	405;"	f
sendto_count	iotrace.c	37;"	m	struct:__anon3	file:
signal_handler	iotrace.c	104;"	f	file:
signal_handler	memtrace.c	155;"	f	file:
size	memtrace.c	41;"	m	struct:__anon2	file:
socket	iotrace.c	265;"	f
start_time	iotrace.c	60;"	v	typeref:struct:timespec	file:
start_time	memtrace.c	29;"	v	typeref:struct:timespec	file:
stats_mutex	iotrace.c	62;"	v	file:
stats_mutex	memtrace.c	31;"	v	file:
store_pointer	memtrace.c	82;"	f	file:
test_different_sizes	iotest.c	128;"	f
test_file_operations	iotest.c	13;"	f
test_multiple_fds	iotest.c	170;"	f
test_socket_operations	iotest.c	45;"	f
total_bytes_written	iotrace.c	39;"	m	struct:__anon3	file:
total_bytes_written	iotrace.c	53;"	v	file:
total_close_count	iotrace.c	48;"	v	file:
total_close_time	iotrace.c	55;"	v	file:
total_free_time	memtrace.c	28;"	v	file:
total_malloc_time	memtrace.c	27;"	v	file:
total_open_count	iotrace.c	46;"	v	file:
total_open_time	iotrace.c	54;"	v	file:
total_send_count	iotrace.c	50;"	v	file:
total_send_time	iotrace.c	41;"	m	struct:__anon3	file:
total_send_time	iotrace.c	58;"	v	file:
total_sendmsg_count	iotrace.c	52;"	v	file:
total_sendto_count	iotrace.c	51;"	v	file:
total_socket_count	iotrace.c	47;"	v	file:
total_socket_time	iotrace.c	56;"	v	file:
total_time	memtrace.c	21;"	m	struct:__anon1	file:
total_write_count	iotrace.c	49;"	v	file:
total_write_time	iotrace.c	40;"	m	struct:__anon3	file:
total_write_time	iotrace.c	57;"	v	file:
write	iotrace.c	337;"	f
write_count	iotrace.c	35;"	m	struct:__anon3	file:
