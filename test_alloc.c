#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <time.h>

#define NUM_ALLOCS 1000
#define MAX_SIZE 0x1000000
#define SLEEP_INTERVAL_US 1000  // 1ms

int main(int argc, char *argv[]) {
    printf("Memory allocation test program\n");
    printf("This program will perform random allocations and frees\n");
    
    srand(time(NULL));
    
    // Array to store allocated pointers
    void *ptrs[NUM_ALLOCS] = {NULL};
    
    // Perform random allocations and frees
    for (int i = 0; i < 100000; i++) {
        // Random index
        int idx = rand() % NUM_ALLOCS;
        
        // Free existing allocation at this index
        if (ptrs[idx] != NULL) {
            free(ptrs[idx]);
            ptrs[idx] = NULL;
        }
        
        // Allocate new memory
        size_t size = rand() % MAX_SIZE + 1;
        ptrs[idx] = malloc(size);
        
        // Touch the memory to ensure it's actually allocated
        if (ptrs[idx]) {
            *((char*)ptrs[idx]) = 1;
        }
        
        // Sleep a bit to control the rate
        usleep(SLEEP_INTERVAL_US);
        
        // Periodically print progress
        if (i % 1000 == 0) {
            printf("Completed %d operations\r", i);
            fflush(stdout);
        }
    }
    
    // Clean up any remaining allocations
    for (int i = 0; i < NUM_ALLOCS; i++) {
        if (ptrs[i] != NULL) {
            free(ptrs[i]);
        }
    }
    
    printf("\nTest completed\n");
    return 0;
} 