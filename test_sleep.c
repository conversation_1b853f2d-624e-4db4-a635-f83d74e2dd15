#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <dlfcn.h>
#include <time.h>
#include <pthread.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include <sys/time.h>
#define NUM_THREADS 1000
#include <math.h>

void* sleep_and_wake_up(void* arg) {
	int thread_id = *(int*)arg;
	while (1){
		// printf("Thread %d is going to sleep for 50 microseconnds...\n", thread_id);
		usleep(50);//睡眠50微秒
		// printf("Thread %d has woken up!\n", threadid);
	}
	return NULL;
}

int main(int argc, char *argv[]) {
	if (argc != 2) {
		printf("Usage: %s <num_threads (1-%d)>\n", argv[0], NUM_THREADS);
		return 1;
	}
	
	int num_threads = atoi(argv[1]);
	if (num_threads < 1 || num_threads > NUM_THREADS) {
		printf("Error: num_threads must be between 1 and %d\n", NUM_THREADS);
		return 1;
	}

	pthread_t threads[NUM_THREADS];
	int thread_ids[NUM_THREADS];
	sleep(3);
	for (int i = 0; i < num_threads; i++){
		thread_ids[i] = i;
		if (pthread_create(&threads[i], NULL, sleep_and_wake_up,&thread_ids[i]) != 0){
			printf("Failed to create thread");
			return 1;
		}
	}
    getchar(); // Wait for user input to keep the main thread alive
	printf("Main thread is running...\n");
}
