commit f95eb6f6bac8c0ce5ece33f6ec6737120c64a7c0
Author: shiyao.zhang <<EMAIL>>
Date:   Wed Jun 18 11:21:22 2025 +0800

    fix(vpf): [J6XOBP-9] Fix qnx build warning
    
    Change-Id: I499ba0e70ba0ad8db005a4188826204e43ff57ad
    Signed-off-by: shiyao.zhang <<EMAIL>>

diff --git a/vpf/vio_debug_qnx.c b/vpf/vio_debug_qnx.c
index 0ad37a36..9cfa4ac9 100644
--- a/vpf/vio_debug_qnx.c
+++ b/vpf/vio_debug_qnx.c
@@ -106,8 +106,7 @@ static ssize_t vpf_fps_store(struct device *dev,
 	vpf_dev = (struct hobot_vpf_dev *)vdevice->ip_dev;
 	vpf_dev->flowid_mask = (u32)simple_strtoul(buf, NULL, 0);
 	if (vpf_dev->flowid_mask == 0u)
-		vpf_dev->flowid_mask = ((u32)1u << VIO_MAX_STREAM) - 1u;
-
+		vpf_dev->flowid_mask = VIO_STREAM_MASK;
 	return (ssize_t)len;
 }
 
@@ -886,7 +885,7 @@ static s32 vpf_fmgr_stats_show(struct seq_file *s, void *unused)
 	u32 i;
 
 	for (i = 0; i < VIO_MAX_STREAM; i++) {
-		len = vio_fmgr_stats(buf, TMP_BUF, i, (u32)1u << i);
+		len = vio_fmgr_stats(buf, TMP_BUF, i, (u64)1u << i);
 		if (len != 0) {
 			seq_printf(s, "%s", buf);
 			(void)memset(buf, 0, TMP_BUF);
